<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Library - Wattatech Enterprise Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-950 text-white font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-gray-900 border-b border-gray-800 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <a href="../index.html" class="text-primary-400 hover:text-primary-300 mr-4">
                            <i class="fas fa-arrow-left"></i>
                        </a>
                        <h1 class="text-xl font-bold text-white">Component Library</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-400">15 Components</span>
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    </div>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Navigation -->
            <nav class="mb-8">
                <div class="flex space-x-1 bg-gray-900 p-1 rounded-lg">
                    <button onclick="showSection('navigation')" class="tab-btn active px-4 py-2 rounded-md text-sm font-medium transition-colors">Navigation</button>
                    <button onclick="showSection('forms')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-colors">Forms</button>
                    <button onclick="showSection('data')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-colors">Data Display</button>
                    <button onclick="showSection('feedback')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-colors">Feedback</button>
                    <button onclick="showSection('layout')" class="tab-btn px-4 py-2 rounded-md text-sm font-medium transition-colors">Layout</button>
                </div>
            </nav>

            <!-- Navigation Components -->
            <div id="navigation" class="component-section">
                <h2 class="text-2xl font-bold text-white mb-6">Navigation Components</h2>
                
                <!-- Header Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Header</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="bg-gray-800 border border-gray-700 rounded-lg">
                            <div class="flex justify-between items-center px-6 py-4">
                                <div class="flex items-center">
                                    <h1 class="text-xl font-bold text-white">Wattatech</h1>
                                    <span class="ml-3 text-sm text-gray-400">Dashboard</span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <button class="text-gray-400 hover:text-white">
                                        <i class="fas fa-bell"></i>
                                    </button>
                                    <div class="flex items-center space-x-2">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
                                             alt="User" class="w-8 h-8 rounded-full">
                                        <span class="text-sm text-white">John Doe</span>
                                        <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Sidebar</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="bg-gray-800 border border-gray-700 rounded-lg w-64">
                            <div class="p-4">
                                <nav class="space-y-2">
                                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 rounded-md">
                                        <i class="fas fa-home mr-3"></i>
                                        Dashboard
                                    </a>
                                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 rounded-md">
                                        <i class="fas fa-phone mr-3"></i>
                                        Calls
                                    </a>
                                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 rounded-md">
                                        <i class="fas fa-users mr-3"></i>
                                        Customers
                                    </a>
                                    <a href="#" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-700 rounded-md">
                                        <i class="fas fa-file-invoice mr-3"></i>
                                        Invoices
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Breadcrumbs Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Breadcrumbs</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <nav class="flex" aria-label="Breadcrumb">
                            <ol class="flex items-center space-x-2">
                                <li>
                                    <a href="#" class="text-gray-400 hover:text-white text-sm">
                                        <i class="fas fa-home"></i>
                                    </a>
                                </li>
                                <li>
                                    <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
                                </li>
                                <li>
                                    <a href="#" class="text-gray-400 hover:text-white text-sm">Customers</a>
                                </li>
                                <li>
                                    <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
                                </li>
                                <li>
                                    <span class="text-white text-sm">John Doe</span>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Form Components -->
            <div id="forms" class="component-section hidden">
                <h2 class="text-2xl font-bold text-white mb-6">Form Components</h2>
                
                <!-- Input Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Input Fields</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Default Input</label>
                            <input type="text" placeholder="Enter text..." class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Input with Icon</label>
                            <div class="relative">
                                <input type="email" placeholder="Enter email..." class="w-full px-4 py-3 pl-10 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <i class="fas fa-envelope absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Error State</label>
                            <input type="text" placeholder="Invalid input..." class="w-full px-4 py-3 bg-gray-800 border border-red-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500">
                            <p class="mt-1 text-sm text-red-400">This field is required</p>
                        </div>
                    </div>
                </div>

                <!-- Button Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Buttons</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex flex-wrap gap-4">
                            <button class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                                Primary
                            </button>
                            <button class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                                Secondary
                            </button>
                            <button class="px-4 py-2 border border-gray-700 text-gray-300 hover:bg-gray-800 rounded-lg transition-colors">
                                Outline
                            </button>
                            <button class="px-4 py-2 text-red-400 hover:bg-red-900/20 rounded-lg transition-colors">
                                Danger
                            </button>
                            <button class="px-4 py-2 bg-primary-600 text-white rounded-lg opacity-50 cursor-not-allowed">
                                Disabled
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Display Components -->
            <div id="data" class="component-section hidden">
                <h2 class="text-2xl font-bold text-white mb-6">Data Display Components</h2>
                
                <!-- Card Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Cards</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-white mb-2">Basic Card</h4>
                                <p class="text-gray-400">This is a basic card component with some content.</p>
                            </div>
                            <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-semibold text-white">Stats Card</h4>
                                    <i class="fas fa-chart-line text-primary-500"></i>
                                </div>
                                <div class="text-3xl font-bold text-white mb-1">1,234</div>
                                <div class="text-sm text-gray-400">Total Calls</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Data Table</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-700">
                                        <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Name</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Email</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Status</th>
                                        <th class="text-left py-3 px-4 text-sm font-medium text-gray-300">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b border-gray-800">
                                        <td class="py-3 px-4 text-sm text-white">John Doe</td>
                                        <td class="py-3 px-4 text-sm text-gray-300"><EMAIL></td>
                                        <td class="py-3 px-4">
                                            <span class="px-2 py-1 bg-green-900/20 text-green-400 rounded-full text-xs">Active</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="text-primary-400 hover:text-primary-300 text-sm">Edit</button>
                                        </td>
                                    </tr>
                                    <tr class="border-b border-gray-800">
                                        <td class="py-3 px-4 text-sm text-white">Jane Smith</td>
                                        <td class="py-3 px-4 text-sm text-gray-300"><EMAIL></td>
                                        <td class="py-3 px-4">
                                            <span class="px-2 py-1 bg-red-900/20 text-red-400 rounded-full text-xs">Inactive</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button class="text-primary-400 hover:text-primary-300 text-sm">Edit</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feedback Components -->
            <div id="feedback" class="component-section hidden">
                <h2 class="text-2xl font-bold text-white mb-6">Feedback Components</h2>
                
                <!-- Notifications -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Notifications</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 space-y-4">
                        <div class="bg-green-900/20 border border-green-700 rounded-lg p-4 flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-3"></i>
                            <span class="text-green-100">Success notification message</span>
                        </div>
                        <div class="bg-red-900/20 border border-red-700 rounded-lg p-4 flex items-center">
                            <i class="fas fa-exclamation-circle text-red-400 mr-3"></i>
                            <span class="text-red-100">Error notification message</span>
                        </div>
                        <div class="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4 flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-400 mr-3"></i>
                            <span class="text-yellow-100">Warning notification message</span>
                        </div>
                        <div class="bg-blue-900/20 border border-blue-700 rounded-lg p-4 flex items-center">
                            <i class="fas fa-info-circle text-blue-400 mr-3"></i>
                            <span class="text-blue-100">Info notification message</span>
                        </div>
                    </div>
                </div>

                <!-- Loading States -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Loading States</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-center space-x-8">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin text-primary-500 text-2xl mb-2"></i>
                                <p class="text-sm text-gray-400">Spinner</p>
                            </div>
                            <div class="text-center">
                                <div class="animate-pulse bg-gray-700 h-4 w-32 rounded mb-2"></div>
                                <p class="text-sm text-gray-400">Skeleton</p>
                            </div>
                            <div class="text-center">
                                <div class="w-8 h-8 border-4 border-primary-500 border-t-transparent rounded-full animate-spin mb-2"></div>
                                <p class="text-sm text-gray-400">Border Spinner</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Layout Components -->
            <div id="layout" class="component-section hidden">
                <h2 class="text-2xl font-bold text-white mb-6">Layout Components</h2>
                
                <!-- Modal Component -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Modal</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <button onclick="openModal('demoModal')" class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                            Open Modal
                        </button>
                    </div>
                </div>

                <!-- Mobile Frame -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-white mb-4">Mobile Frame</h3>
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex justify-center">
                            <div class="mobile-frame" style="transform: scale(0.5); transform-origin: top;">
                                <div class="mobile-screen">
                                    <div class="mobile-status-bar">
                                        <span>9:41</span>
                                        <span class="flex items-center space-x-1">
                                            <i class="fas fa-signal text-xs"></i>
                                            <i class="fas fa-wifi text-xs"></i>
                                            <i class="fas fa-battery-three-quarters text-xs"></i>
                                        </span>
                                    </div>
                                    <div class="mobile-content bg-gray-900 flex items-center justify-center">
                                        <div class="text-center">
                                            <i class="fas fa-mobile-alt text-primary-500 text-4xl mb-4"></i>
                                            <p class="text-white">Mobile App Frame</p>
                                        </div>
                                    </div>
                                    <div class="mobile-home-indicator"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Modal -->
    <div id="demoModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-white">Demo Modal</h3>
                <button onclick="closeModal('demoModal')" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p class="text-gray-300 mb-4">This is a demo modal component.</p>
            <div class="flex justify-end space-x-3">
                <button onclick="closeModal('demoModal')" class="px-4 py-2 border border-gray-700 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors">
                    Cancel
                </button>
                <button class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                    Confirm
                </button>
            </div>
        </div>
    </div>

    <script src="../assets/js/app.js"></script>
    <script>
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.component-section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-primary-600', 'text-white');
                btn.classList.add('text-gray-400', 'hover:text-white');
            });
            
            // Show selected section
            document.getElementById(sectionId).classList.remove('hidden');
            
            // Add active class to selected tab
            event.target.classList.add('active', 'bg-primary-600', 'text-white');
            event.target.classList.remove('text-gray-400', 'hover:text-white');
        }

        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.getElementById(modalId).classList.add('flex');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.getElementById(modalId).classList.remove('flex');
        }

        // Initialize first tab as active
        document.addEventListener('DOMContentLoaded', function() {
            const firstTab = document.querySelector('.tab-btn');
            firstTab.classList.add('active', 'bg-primary-600', 'text-white');
            firstTab.classList.remove('text-gray-400');
        });
    </script>
</body>
</html>
