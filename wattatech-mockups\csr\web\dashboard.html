<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSR Dashboard - Wattatech Enterprise Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
    <style>
        .phone-ui {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            background: linear-gradient(145deg, #1f2937, #111827);
            border: 1px solid #374151;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .phone-ui.minimized {
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }
        .phone-ui.call-active {
            border-color: #10b981;
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body class="bg-gray-950 text-white font-sans">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-900 border-r border-gray-800 flex flex-col">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-800">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-headset text-white text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-white">Wattatech</h1>
                        <p class="text-xs text-gray-400">CSR Dashboard</p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 p-4">
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.html" class="flex items-center px-3 py-2 text-sm font-medium text-white bg-primary-600 rounded-lg">
                            <i class="fas fa-home mr-3"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="calls/history.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg">
                            <i class="fas fa-phone mr-3"></i>
                            Call History
                        </a>
                    </li>
                    <li>
                        <a href="calls/callbacks.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg">
                            <i class="fas fa-clock mr-3"></i>
                            Callbacks
                            <span class="ml-auto bg-red-600 text-white text-xs px-2 py-1 rounded-full">3</span>
                        </a>
                    </li>
                    <li>
                        <a href="customers/list.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg">
                            <i class="fas fa-users mr-3"></i>
                            Customers
                        </a>
                    </li>
                    <li>
                        <a href="leads/pipeline.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg">
                            <i class="fas fa-chart-line mr-3"></i>
                            Leads
                        </a>
                    </li>
                    <li>
                        <a href="services/requests.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg">
                            <i class="fas fa-tools mr-3"></i>
                            Service Requests
                        </a>
                    </li>
                    <li>
                        <a href="performance/metrics.html" class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg">
                            <i class="fas fa-chart-bar mr-3"></i>
                            Performance
                        </a>
                    </li>
                </ul>
            </nav>

            <!-- Status -->
            <div class="p-4 border-t border-gray-800">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-300">Available</span>
                    </div>
                    <button onclick="toggleStatus()" class="text-xs text-gray-400 hover:text-white">
                        Change
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-gray-900 border-b border-gray-800 px-6 py-4">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-xl font-semibold text-white">Dashboard</h2>
                        <p class="text-sm text-gray-400">Welcome back, David Thompson</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button class="relative text-gray-400 hover:text-white">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                        </button>
                        <div class="flex items-center space-x-2">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
                                 alt="User" class="w-8 h-8 rounded-full">
                            <span class="text-sm text-white">David Thompson</span>
                            <i class="fas fa-chevron-down text-gray-400 text-xs"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Today's Calls</p>
                                <p class="text-2xl font-bold text-white">23</p>
                                <p class="text-xs text-green-400">+12% from yesterday</p>
                            </div>
                            <div class="w-12 h-12 bg-primary-600/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-phone text-primary-500"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Avg Handle Time</p>
                                <p class="text-2xl font-bold text-white">4:23</p>
                                <p class="text-xs text-red-400">+0:15 from target</p>
                            </div>
                            <div class="w-12 h-12 bg-yellow-600/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock text-yellow-500"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Conversions</p>
                                <p class="text-2xl font-bold text-white">68%</p>
                                <p class="text-xs text-green-400">+5% from last week</p>
                            </div>
                            <div class="w-12 h-12 bg-green-600/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-green-500"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Revenue</p>
                                <p class="text-2xl font-bold text-white">$2,450</p>
                                <p class="text-xs text-green-400">+18% this month</p>
                            </div>
                            <div class="w-12 h-12 bg-emerald-600/20 rounded-lg flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-emerald-500"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity & Quick Actions -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Recent Calls -->
                    <div class="bg-gray-900 border border-gray-800 rounded-lg">
                        <div class="p-6 border-b border-gray-800">
                            <h3 class="text-lg font-semibold text-white">Recent Calls</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-green-600/20 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-phone text-green-500 text-sm"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-white">Robert Anderson</p>
                                            <p class="text-xs text-gray-400">+**************** • 5:23</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-green-900/20 text-green-400 rounded-full text-xs">Sale</span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-yellow-600/20 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-phone text-yellow-500 text-sm"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-white">Jennifer Martinez</p>
                                            <p class="text-xs text-gray-400">+**************** • 2:15</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-yellow-900/20 text-yellow-400 rounded-full text-xs">Callback</span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-600/20 rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-phone text-blue-500 text-sm"></i>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-white">William Davis</p>
                                            <p class="text-xs text-gray-400">+**************** • 3:45</p>
                                        </div>
                                    </div>
                                    <span class="px-2 py-1 bg-blue-900/20 text-blue-400 rounded-full text-xs">Lead</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-gray-900 border border-gray-800 rounded-lg">
                        <div class="p-6 border-b border-gray-800">
                            <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 gap-4">
                                <button onclick="makeCall()" class="p-4 bg-primary-600 hover:bg-primary-700 rounded-lg text-center transition-colors">
                                    <i class="fas fa-phone text-white text-xl mb-2 block"></i>
                                    <span class="text-sm text-white">Make Call</span>
                                </button>
                                <button onclick="openCustomerSearch()" class="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg text-center transition-colors">
                                    <i class="fas fa-search text-gray-300 text-xl mb-2 block"></i>
                                    <span class="text-sm text-gray-300">Find Customer</span>
                                </button>
                                <button onclick="createServiceRequest()" class="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg text-center transition-colors">
                                    <i class="fas fa-plus text-gray-300 text-xl mb-2 block"></i>
                                    <span class="text-sm text-gray-300">New Service</span>
                                </button>
                                <button onclick="viewCallbacks()" class="p-4 bg-gray-800 hover:bg-gray-700 rounded-lg text-center transition-colors relative">
                                    <i class="fas fa-clock text-gray-300 text-xl mb-2 block"></i>
                                    <span class="text-sm text-gray-300">Callbacks</span>
                                    <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Callbacks -->
                <div class="bg-gray-900 border border-gray-800 rounded-lg">
                    <div class="p-6 border-b border-gray-800">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-white">Pending Callbacks</h3>
                            <a href="calls/callbacks.html" class="text-sm text-primary-400 hover:text-primary-300">View all</a>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-800">
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-300">Customer</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-300">Scheduled</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-300">Priority</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-300">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-800">
                                    <td class="py-3 px-6">
                                        <div>
                                            <p class="text-sm font-medium text-white">Sarah Johnson</p>
                                            <p class="text-xs text-gray-400">+1 (555) 456-7890</p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-6 text-sm text-gray-300">Today 2:00 PM</td>
                                    <td class="py-3 px-6">
                                        <span class="px-2 py-1 bg-red-900/20 text-red-400 rounded-full text-xs">High</span>
                                    </td>
                                    <td class="py-3 px-6">
                                        <button onclick="callCustomer('************')" class="text-primary-400 hover:text-primary-300 text-sm mr-3">
                                            <i class="fas fa-phone mr-1"></i> Call
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300 text-sm">
                                            <i class="fas fa-edit mr-1"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-800">
                                    <td class="py-3 px-6">
                                        <div>
                                            <p class="text-sm font-medium text-white">Michael Brown</p>
                                            <p class="text-xs text-gray-400">+****************</p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-6 text-sm text-gray-300">Today 3:30 PM</td>
                                    <td class="py-3 px-6">
                                        <span class="px-2 py-1 bg-yellow-900/20 text-yellow-400 rounded-full text-xs">Medium</span>
                                    </td>
                                    <td class="py-3 px-6">
                                        <button onclick="callCustomer('************')" class="text-primary-400 hover:text-primary-300 text-sm mr-3">
                                            <i class="fas fa-phone mr-1"></i> Call
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300 text-sm">
                                            <i class="fas fa-edit mr-1"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-3 px-6">
                                        <div>
                                            <p class="text-sm font-medium text-white">Lisa Chen</p>
                                            <p class="text-xs text-gray-400">+****************</p>
                                        </div>
                                    </td>
                                    <td class="py-3 px-6 text-sm text-gray-300">Tomorrow 10:00 AM</td>
                                    <td class="py-3 px-6">
                                        <span class="px-2 py-1 bg-blue-900/20 text-blue-400 rounded-full text-xs">Low</span>
                                    </td>
                                    <td class="py-3 px-6">
                                        <button onclick="callCustomer('************')" class="text-primary-400 hover:text-primary-300 text-sm mr-3">
                                            <i class="fas fa-phone mr-1"></i> Call
                                        </button>
                                        <button class="text-gray-400 hover:text-gray-300 text-sm">
                                            <i class="fas fa-edit mr-1"></i> Edit
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Floating Phone UI -->
    <div id="phoneUI" class="phone-ui">
        <div class="p-4">
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-sm font-semibold text-white">Phone</h4>
                <button onclick="togglePhoneUI()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
            
            <div id="phoneIdle" class="text-center">
                <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-phone text-white text-xl"></i>
                </div>
                <p class="text-sm text-gray-300 mb-4">Ready to take calls</p>
                <button onclick="simulateIncomingCall()" class="w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors">
                    <i class="fas fa-phone mr-2"></i>
                    Simulate Incoming Call
                </button>
            </div>

            <div id="phoneActive" class="hidden text-center">
                <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                    <i class="fas fa-phone text-white text-xl"></i>
                </div>
                <p class="text-sm text-white font-medium mb-1">William Davis</p>
                <p class="text-xs text-gray-400 mb-2">+****************</p>
                <p class="text-xs text-gray-400 mb-4" id="callTimer">00:00</p>
                
                <div class="grid grid-cols-3 gap-2 mb-4">
                    <button onclick="muteCall()" class="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm">
                        <i class="fas fa-microphone-slash"></i>
                    </button>
                    <button onclick="holdCall()" class="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button onclick="transferCall()" class="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
                
                <button onclick="endCall()" class="w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors">
                    <i class="fas fa-phone-slash mr-2"></i>
                    End Call
                </button>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        let callTimer = 0;
        let callInterval;

        function togglePhoneUI() {
            const phoneUI = document.getElementById('phoneUI');
            phoneUI.classList.toggle('minimized');
        }

        function simulateIncomingCall() {
            document.getElementById('phoneIdle').classList.add('hidden');
            document.getElementById('phoneActive').classList.remove('hidden');
            document.getElementById('phoneUI').classList.add('call-active');
            
            // Start call timer
            callTimer = 0;
            callInterval = setInterval(() => {
                callTimer++;
                const minutes = Math.floor(callTimer / 60);
                const seconds = callTimer % 60;
                document.getElementById('callTimer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function endCall() {
            document.getElementById('phoneActive').classList.add('hidden');
            document.getElementById('phoneIdle').classList.remove('hidden');
            document.getElementById('phoneUI').classList.remove('call-active');
            
            clearInterval(callInterval);
            
            // Show call disposition modal (simulate)
            setTimeout(() => {
                alert('Call ended. Disposition: Sale - HVAC maintenance scheduled');
            }, 500);
        }

        function muteCall() {
            const button = event.target.closest('button');
            button.classList.toggle('bg-red-600');
            button.classList.toggle('bg-gray-700');
        }

        function holdCall() {
            alert('Call placed on hold');
        }

        function transferCall() {
            alert('Transfer call to on-site agent');
        }

        function makeCall() {
            const phoneNumber = prompt('Enter phone number:');
            if (phoneNumber) {
                simulateIncomingCall();
            }
        }

        function callCustomer(number) {
            simulateIncomingCall();
        }

        function openCustomerSearch() {
            window.location.href = 'customers/search.html';
        }

        function createServiceRequest() {
            window.location.href = 'services/create.html';
        }

        function viewCallbacks() {
            window.location.href = 'calls/callbacks.html';
        }

        function toggleStatus() {
            const statuses = ['Available', 'Busy', 'Break', 'Offline'];
            const colors = ['green', 'red', 'yellow', 'gray'];
            // Simulate status change
            alert('Status changed to: Busy');
        }
    </script>
</body>
</html>
