<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSR Dashboard - Wattatech Mobile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-950 text-white font-sans">
    <div class="flex justify-center items-center min-h-screen p-4">
        <div class="mobile-frame">
            <div class="mobile-screen">
                <!-- Mobile Status Bar -->
                <div class="mobile-status-bar">
                    <span>9:41</span>
                    <span class="flex items-center space-x-1">
                        <i class="fas fa-signal text-xs"></i>
                        <i class="fas fa-wifi text-xs"></i>
                        <i class="fas fa-battery-three-quarters text-xs"></i>
                    </span>
                </div>

                <!-- Mobile Content -->
                <div class="mobile-content bg-gray-900">
                    <!-- Header -->
                    <div class="bg-gray-800 px-4 py-3 border-b border-gray-700">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-headset text-white text-sm"></i>
                                </div>
                                <div>
                                    <h1 class="text-sm font-bold text-white">CSR Dashboard</h1>
                                    <p class="text-xs text-gray-400">David Thompson</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                                    <span class="text-xs text-gray-300">Available</span>
                                </div>
                                <button class="text-gray-400">
                                    <i class="fas fa-bell text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Stats Cards -->
                    <div class="p-4">
                        <div class="grid grid-cols-2 gap-3 mb-4">
                            <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xs text-gray-400">Today's Calls</p>
                                        <p class="text-lg font-bold text-white">23</p>
                                    </div>
                                    <i class="fas fa-phone text-primary-500"></i>
                                </div>
                            </div>
                            <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xs text-gray-400">Conversions</p>
                                        <p class="text-lg font-bold text-white">68%</p>
                                    </div>
                                    <i class="fas fa-chart-line text-green-500"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mb-4">
                            <h3 class="text-sm font-semibold text-white mb-3">Quick Actions</h3>
                            <div class="grid grid-cols-4 gap-3">
                                <button onclick="makeCall()" class="p-3 bg-primary-600 rounded-lg text-center">
                                    <i class="fas fa-phone text-white text-lg mb-1 block"></i>
                                    <span class="text-xs text-white">Call</span>
                                </button>
                                <button onclick="openSearch()" class="p-3 bg-gray-800 rounded-lg text-center">
                                    <i class="fas fa-search text-gray-300 text-lg mb-1 block"></i>
                                    <span class="text-xs text-gray-300">Search</span>
                                </button>
                                <button onclick="viewCallbacks()" class="p-3 bg-gray-800 rounded-lg text-center relative">
                                    <i class="fas fa-clock text-gray-300 text-lg mb-1 block"></i>
                                    <span class="text-xs text-gray-300">Callbacks</span>
                                    <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                                </button>
                                <button onclick="createService()" class="p-3 bg-gray-800 rounded-lg text-center">
                                    <i class="fas fa-plus text-gray-300 text-lg mb-1 block"></i>
                                    <span class="text-xs text-gray-300">Service</span>
                                </button>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="mb-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-semibold text-white">Recent Calls</h3>
                                <button class="text-xs text-primary-400">View All</button>
                            </div>
                            <div class="space-y-3">
                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-green-600/20 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-phone text-green-500 text-xs"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-white">Robert Anderson</p>
                                                <p class="text-xs text-gray-400">5:23 • 10:15 AM</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 bg-green-900/20 text-green-400 rounded-full text-xs">Sale</span>
                                    </div>
                                </div>

                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-yellow-600/20 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-phone text-yellow-500 text-xs"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-white">Jennifer Martinez</p>
                                                <p class="text-xs text-gray-400">2:15 • 9:30 AM</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 bg-yellow-900/20 text-yellow-400 rounded-full text-xs">Callback</span>
                                    </div>
                                </div>

                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-blue-600/20 rounded-full flex items-center justify-center mr-3">
                                                <i class="fas fa-phone text-blue-500 text-xs"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm font-medium text-white">William Davis</p>
                                                <p class="text-xs text-gray-400">3:45 • 8:45 AM</p>
                                            </div>
                                        </div>
                                        <span class="px-2 py-1 bg-blue-900/20 text-blue-400 rounded-full text-xs">Lead</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pending Callbacks -->
                        <div class="mb-20">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-semibold text-white">Pending Callbacks</h3>
                                <button class="text-xs text-primary-400">View All</button>
                            </div>
                            <div class="space-y-3">
                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <div>
                                            <p class="text-sm font-medium text-white">Sarah Johnson</p>
                                            <p class="text-xs text-gray-400">Today 2:00 PM</p>
                                        </div>
                                        <span class="px-2 py-1 bg-red-900/20 text-red-400 rounded-full text-xs">High</span>
                                    </div>
                                    <button onclick="callCustomer('************')" class="w-full py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg text-sm transition-colors">
                                        <i class="fas fa-phone mr-2"></i>
                                        Call Now
                                    </button>
                                </div>

                                <div class="bg-gray-800 border border-gray-700 rounded-lg p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <div>
                                            <p class="text-sm font-medium text-white">Michael Brown</p>
                                            <p class="text-xs text-gray-400">Today 3:30 PM</p>
                                        </div>
                                        <span class="px-2 py-1 bg-yellow-900/20 text-yellow-400 rounded-full text-xs">Medium</span>
                                    </div>
                                    <button onclick="callCustomer('************')" class="w-full py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg text-sm transition-colors">
                                        <i class="fas fa-phone mr-2"></i>
                                        Call Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Navigation -->
                    <div class="fixed bottom-0 left-0 right-0 bg-gray-800 border-t border-gray-700" style="margin: 0 8px 8px 8px; border-radius: 16px 16px 0 0;">
                        <div class="grid grid-cols-5 py-2">
                            <button class="p-3 text-center">
                                <i class="fas fa-home text-primary-500 text-lg mb-1 block"></i>
                                <span class="text-xs text-primary-500">Dashboard</span>
                            </button>
                            <button onclick="openCalls()" class="p-3 text-center">
                                <i class="fas fa-phone text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Calls</span>
                            </button>
                            <button onclick="openCustomers()" class="p-3 text-center">
                                <i class="fas fa-users text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Customers</span>
                            </button>
                            <button onclick="openLeads()" class="p-3 text-center">
                                <i class="fas fa-chart-line text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Leads</span>
                            </button>
                            <button onclick="openProfile()" class="p-3 text-center">
                                <i class="fas fa-user text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Profile</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Home Indicator -->
                <div class="mobile-home-indicator"></div>
            </div>
        </div>
    </div>

    <!-- Call Modal -->
    <div id="callModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-gray-900 border border-gray-800 rounded-2xl p-6 max-w-sm w-full mx-4">
            <div class="text-center">
                <div class="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                    <i class="fas fa-phone text-white text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-white mb-1">William Davis</h3>
                <p class="text-sm text-gray-400 mb-2">+****************</p>
                <p class="text-sm text-gray-400 mb-6" id="mobileCallTimer">00:00</p>
                
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <button onclick="muteCall()" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-full text-white">
                        <i class="fas fa-microphone-slash"></i>
                    </button>
                    <button onclick="holdCall()" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-full text-white">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button onclick="transferCall()" class="p-3 bg-gray-700 hover:bg-gray-600 rounded-full text-white">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
                
                <button onclick="endCall()" class="w-full py-3 bg-red-600 hover:bg-red-700 text-white rounded-full text-sm transition-colors">
                    <i class="fas fa-phone-slash mr-2"></i>
                    End Call
                </button>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        let mobileCallTimer = 0;
        let mobileCallInterval;

        function makeCall() {
            const phoneNumber = prompt('Enter phone number:');
            if (phoneNumber) {
                startCall();
            }
        }

        function callCustomer(number) {
            startCall();
        }

        function startCall() {
            document.getElementById('callModal').classList.remove('hidden');
            document.getElementById('callModal').classList.add('flex');
            
            // Start call timer
            mobileCallTimer = 0;
            mobileCallInterval = setInterval(() => {
                mobileCallTimer++;
                const minutes = Math.floor(mobileCallTimer / 60);
                const seconds = mobileCallTimer % 60;
                document.getElementById('mobileCallTimer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function endCall() {
            document.getElementById('callModal').classList.add('hidden');
            document.getElementById('callModal').classList.remove('flex');
            
            clearInterval(mobileCallInterval);
            
            // Show call disposition
            setTimeout(() => {
                alert('Call ended. Disposition: Sale - HVAC maintenance scheduled');
            }, 500);
        }

        function muteCall() {
            const button = event.target.closest('button');
            button.classList.toggle('bg-red-600');
            button.classList.toggle('bg-gray-700');
        }

        function holdCall() {
            alert('Call placed on hold');
        }

        function transferCall() {
            alert('Transfer call to on-site agent');
        }

        function openSearch() {
            window.location.href = 'customers/search.html';
        }

        function viewCallbacks() {
            window.location.href = 'calls/callbacks.html';
        }

        function createService() {
            window.location.href = 'services/create.html';
        }

        function openCalls() {
            window.location.href = 'calls/history.html';
        }

        function openCustomers() {
            window.location.href = 'customers/list.html';
        }

        function openLeads() {
            window.location.href = 'leads/pipeline.html';
        }

        function openProfile() {
            window.location.href = 'profile/view.html';
        }

        // Add mobile-specific touch interactions
        document.addEventListener('touchstart', function(e) {
            if (e.target.matches('button, .btn')) {
                e.target.style.transform = 'scale(0.95)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.matches('button, .btn')) {
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 100);
            }
        });
    </script>
</body>
</html>
