<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wattatech Enterprise Platform - Mockup System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                            950: '#172554'
                        },
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                            950: '#030712'
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .mobile-frame {
            width: 375px;
            height: 812px;
            background: #1f2937;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            position: relative;
        }
        .mobile-screen {
            width: 100%;
            height: 100%;
            background: #111827;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .mobile-notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #1f2937;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }
        .mobile-home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
    </style>
</head>
<body class="bg-gray-950 text-white font-sans">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-gray-900 border-b border-gray-800">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <h1 class="text-2xl font-bold text-white">Wattatech</h1>
                            <p class="text-sm text-gray-400">Enterprise Platform Mockups</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-400">v2.0.0</span>
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-white mb-4">Complete Enterprise System Mockups</h2>
                <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                    Comprehensive HTML mockup system covering all 7 user roles with 155+ pages, 
                    featuring dual-interface architecture (web + mobile) and dark theme design system.
                </p>
            </div>

            <!-- User Roles Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                <!-- Customer -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-user text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Customer</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Service requests and payments</p>
                    <div class="flex space-x-2">
                        <a href="customer/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="customer/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">12 pages</div>
                </div>

                <!-- On-Site Agent -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-tools text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">On-Site Agent</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Field service delivery</p>
                    <div class="flex space-x-2">
                        <a href="onsite-agent/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="onsite-agent/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">15 pages</div>
                </div>

                <!-- On-Site Owner -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-map-marked-alt text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">On-Site Owner</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Regional management</p>
                    <div class="flex space-x-2">
                        <a href="onsite-owner/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="onsite-owner/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">16 pages</div>
                </div>

                <!-- CSR -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-headset text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">CSR</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Call handling & CRM</p>
                    <div class="flex space-x-2">
                        <a href="csr/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="csr/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">20 pages</div>
                </div>

                <!-- Team Leader -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-users text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Team Leader</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Monitoring & supervision</p>
                    <div class="flex space-x-2">
                        <a href="team-leader/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="team-leader/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">18 pages</div>
                </div>

                <!-- Remote Site Admin -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-building text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Site Admin</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Business management</p>
                    <div class="flex space-x-2">
                        <a href="site-admin/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="site-admin/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">22 pages</div>
                </div>

                <!-- Super Admin -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-crown text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Super Admin</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Platform management</p>
                    <div class="flex space-x-2">
                        <a href="super-admin/web/dashboard.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="super-admin/mobile/dashboard.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">25 pages</div>
                </div>

                <!-- Authentication & System -->
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 hover:border-primary-500 transition-colors">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-shield-alt text-primary-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Auth & System</h3>
                    </div>
                    <p class="text-gray-400 text-sm mb-4">Authentication & errors</p>
                    <div class="flex space-x-2">
                        <a href="auth/web/login.html" class="flex-1 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Web View
                        </a>
                        <a href="auth/mobile/login.html" class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm text-center transition-colors">
                            Mobile App
                        </a>
                    </div>
                    <div class="mt-2 text-xs text-gray-500">12 pages</div>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
                    <div class="text-3xl font-bold text-primary-500 mb-2">155+</div>
                    <div class="text-gray-400">Total Pages</div>
                </div>
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
                    <div class="text-3xl font-bold text-primary-500 mb-2">7</div>
                    <div class="text-gray-400">User Roles</div>
                </div>
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
                    <div class="text-3xl font-bold text-primary-500 mb-2">15</div>
                    <div class="text-gray-400">Components</div>
                </div>
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6 text-center">
                    <div class="text-3xl font-bold text-primary-500 mb-2">2</div>
                    <div class="text-gray-400">Interfaces</div>
                </div>
            </div>

            <!-- Additional Resources -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6">
                    <h3 class="text-lg font-semibold mb-4">Component Library</h3>
                    <p class="text-gray-400 mb-4">Reusable UI components for consistent design</p>
                    <a href="components/index.html" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded transition-colors">
                        View Components
                    </a>
                </div>
                <div class="bg-gray-900 rounded-lg border border-gray-800 p-6">
                    <h3 class="text-lg font-semibold mb-4">Documentation</h3>
                    <p class="text-gray-400 mb-4">Implementation guide and specifications</p>
                    <a href="docs/index.html" class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded transition-colors">
                        View Docs
                    </a>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
