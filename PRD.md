# Instructions for creating a product requirements document (PRD)

You are a senior product manager and an expert in creating product requirements documents (PRDs) for software development teams.

Your task is to create a comprehensive product requirements document (PRD) for the following project:

<prd_instructions>

The PRD is intended to feed into an AI co-pilot or coding agent to autonomously build the software project.

</prd_instructions>

Follow these steps to create the PRD:

<steps>
  
1. Begin with a brief overview explaining the project and the purpose of the document.
  
2. Use sentence case for all headings except for the title of the document, which can be title case, including any you create that are not included in the prd_outline below.
  
3. Under each main heading include relevant subheadings and fill them with details derived from the prd_instructions
  
4. Organize your PRD into the sections as shown in the prd_outline below
  
5. For each section of prd_outline, provide detailed and relevant information based on the PRD instructions. Ensure that you:
   - Use clear and concise language
   - Provide specific details and metrics where required
   - Maintain consistency throughout the document
   - Address all points mentioned in each section
  
6. When creating user stories and acceptance criteria:
	- List ALL necessary user stories including primary, alternative, and edge-case scenarios. 
	- Assign a unique requirement ID (e.g., US-001) to each user story for direct traceability
	- Include at least one user story specifically for secure access or authentication if the application requires user identification or access restrictions
	- Ensure no potential user interaction is omitted
	- Make sure each user story is testable
	- Review the user_story example below for guidance on how to structure your user stories
  
7. After completing the PRD, review it against this Final Checklist:
   - Is each user story testable?
   - Are acceptance criteria clear and specific?
   - Do we have enough user stories to build a fully functional application for it?
   - Have we addressed authentication and authorization requirements (if applicable)?
  
8. Format your PRD:
   - Maintain consistent formatting and numbering.
  	- Do not use dividers or horizontal rules in the output.
  	- List ALL User Stories in the output!
	  - Format the PRD in valid Markdown, with no extraneous disclaimers.
	  - Do not add a conclusion or footer. The user_story section is the last section.
	  - Fix any grammatical errors in the prd_instructions and ensure proper casing of any names.
	  - When referring to the project, do not use project_title. Instead, refer to it in a more simple and conversational way. For example, "the project", "this tool" etc.
  
</steps>

<prd_outline>

# PRD: Wattatech

## 1. Product overview
### 1.1 Document title and version
- PRD: Wattatech
- Version: 1.0.0

### 1.2 Product summary
Wattatech is a PWA web app designed as a one-stop answering service for service-based businesses. It handles both inbound and outbound customer calls, service tracking, hourly and fixed service invoicing, and agent workflows. In-house agents (on salary) handle customer service remotely or transfer to on-site agents (working on commission) based on need.

The system includes features like call handling, callbacks, call logs, CRM, time tracking, sales marking, and lead conversion. Twilio powers browser-based calling, while Stripe manages payments. On-site agents are assigned specific areas and connect via GMB numbers, receiving commissions based on invoice outcomes. Manual or automated payouts are available.

The stack includes Vue.js (with Vite), Node.js microservices, Dockerized deployment, GitHub for CI/CD, Firebase for push notifications, and Stripe for transactions.

## 2. Goals
### 2.1 Business goals
- Centralize communication and service delivery for service-based businesses.
- Reduce operational costs by automating call handling and service delegation.
- Enable scalable growth by using commission-based on-site agents tied to local GMB listings.
- Maximize call-to-sale conversions via in-app invoicing and subscription offers.
- Ensure transparency in agent commissions, invoicing, and revenue distribution.
- Integrate all business communication (calls, SMS, CRM) into a single web-based interface.

### 2.2 User goals
- In-house agents want to handle multiple calls efficiently using browser-based tools.
- On-site agents want fair commission tracking and real-time service assignments.
- Team leaders want visibility into agent activity and call resolution metrics.
- Customers want quick service resolutions—either remotely or via in-person visits.
- Site owners want automatic call routing, invoice tracking, and simple payouts.

### 2.3 Non-goals
- Providing hardware device support for agents or customers.
- Running a third-party dispatch system not integrated with the Wattatech ecosystem.
- Handling international currencies and multi-language support in v1.0.0.
- Offering native mobile apps (covered via PWA instead).
- Supporting multiple payment gateways beyond Stripe in initial launch.

## 3. User personas
### 3.1 Key user types
- Super Admin (Platform Owner)
- Remote Site Admin
- Team Leader
- CSR (Remote Agent)
- On-Site Owner
- On-Site Agent
- Customers

### 3.2 Basic persona details
- **Super Admin**: Manages the entire platform. Sets global policies, handles disputes, controls platform-level settings, and oversees billing infrastructure.
- **Remote Site Admin**: Manages a specific remote answering site and its users. Owns the GMB listing and receives routed calls. Oversees service performance and commissions.
- **Team Leader**: Supervises CSRs, monitors call activities, tracks KPIs, and ensures SLAs are met within their assigned site.
- **CSR (Remote Agent)**: Responds to inbound calls, attempts to resolve customer queries remotely, raises invoices, and initiates call transfers when necessary.
- **On-Site Owner**: Owns service coverage for a specific region. Can respond directly or assign on-site agents. Tracks earnings and service completion rates.
- **On-Site Agent**: Visits customers for physical service, tracks service time, completes invoices, and finalizes sales on-site. Receives commission payouts.
- **Customer**: Calls support numbers via GMB listings, receives either remote or on-site assistance, and pays via Stripe for one-time or subscription-based services.

### 3.3 Role-based access
- **Super Admin**: Full access to platform-wide settings, logs, reports, payouts, and admin controls.
- **Remote Site Admin**: Manages agents and leaders, sets team schedules, links GMB, and views earnings/invoice breakdowns.
- **Team Leader**: Views team dashboards, agent status, call logs, and performance reports. Limited editing rights.
- **CSR**: Can answer and initiate calls, log interactions, raise invoices, pause/resume duty, and mark callbacks or completed tasks.
- **On-Site Owner**: Manages physical service requests, assigns on-site agents, and has visibility over all invoices and agent performance under their GMB listing.
- **On-Site Agent**: Can accept service requests, track time, raise invoices, and complete sales. Can view earnings and set availability.
- **Customer**: Can call, receive assistance, approve/decline services, pay for services, and view invoices or service history.

## 4. Functional requirements
- **Inbound & Outbound Calling** (Priority: High)
  - CSR can receive calls through Twilio integrated browser dialer.
  - CSR can initiate callback or outgoing calls.
  - GMB numbers route to the respective CSR pool under Remote Site Admin.
- **Call Logs & CRM** (Priority: High)
  - Each call is logged with timestamp, agent, duration, and notes.
  - CRM shows linked calls, customer details, and past service history.
- **Lead Management & Conversion** (Priority: High)
  - Calls can be marked as lead, sale, or callback.
  - Callback queue managed by CSRs or team leaders.
- **Invoicing & Service Tracking** (Priority: High)
  - Agents can generate invoices for hourly or fixed-price services.
  - Invoice links include payment buttons via Stripe.
- **Team Supervision Tools** (Priority: Medium)
  - Team Leaders can monitor live status of agents (available, paused, wrap-up).
  - View KPI dashboards for site performance.
- **Agent Time Tracking** (Priority: Medium)
  - On-site agents log service start/end times.
  - CSR shifts show pause, wrap, login durations.
- **Commission & Payouts** (Priority: High)
  - System auto-calculates 20%/80% commission splits.
  - Payouts can be manual or automatic, preferences stored per agent.
- **Push Notifications** (Priority: Medium)
  - Send updates to agents for new calls, callbacks, invoice status.
- **Authentication & Role-Based Access** (Priority: High)
  - Secure login based on user role.
  - Super Admin, Remote Site Admin, CSR, On-Site Owner, and Customers each have different dashboards.
- **Progressive Web App (PWA)** (Priority: High)
  - Works on mobile and desktop with install capability.
  - Offline fallback for data entry, synced once online.

## 5. User experience
### 5.1. Entry points & first-time user flow
- Super Admin sets up company instance, configures services, pricing, knowledge base
- On-Site Owner enters personal and business details and gets verified for GMB
- On-Site Sub-Agents receive invites and sign up
- Customers sign up via email/password or social login
- Remote Site Admin, Team Leader, and CSRs are invited by higher roles and onboard accordingly

### 5.2. Core experience
- CSR logs in to CRM with floating Phone UI, manages calls, dispositions, and views performance reports
- Customers browse service history, request support, or pay invoices
- On-Site Agent accepts jobs, sets appointments, starts/stops service time, and raises invoices

### 5.3. Advanced features & edge cases
- CSR transfers or conferences calls with on-site agents
- Offline sync for service logs
- Push alerts for callbacks, missed calls
- Auto-resume call state on page refresh

### 5.4. UI/UX highlights
- Persistent browser-based phone UI (Twilio)
- Call wrap-up and disposition screen
- Internal chat between roles
- Responsive PWA with floating notifications
- Role-based dashboard styling and controls

## 6. Narrative
Anjali is a homeowner needing urgent appliance repair. She wants to talk to someone right away because she’s unsure if it needs a service visit or not. She finds a local GMB listing and calls the support number. A friendly agent on Wattatech answers instantly, solves her issue remotely, and offers a one-time payment option via Stripe. When a visit is needed, she gets a confirmed time from a field technician. She receives prompt service and a transparent invoice—no follow-up needed.

## 7. Success metrics
### 7.1. User-centric metrics
- Average time to first response from CSR after customer call
- Callback resolution time (from request to call back)
- Customer satisfaction score (CSAT) via post-service feedback
- Number of completed services per on-site agent/month
- Average CSR handling time per call

### 7.2. Business metrics
- Conversion rate from inbound calls to successful sales/invoices
- Revenue split analytics (total payout vs company revenue)
- Number of active agents (remote and on-site)
- Invoice payment success rate (via Stripe)
- Total number of services created per business category

### 7.3. Technical metrics
- Uptime of PWA application and microservices
- Call connectivity success rate via Twilio
- Sync success rate (offline > online for PWA)
- Page load time (target < 2s for main dashboard)
- CI/CD pipeline success rate (auto-deploys from GitHub)

## 8. Technical considerations
### 8.1. Integration points
- Google Maps API
- Firebase for push notifications
- Twilio for call/SMS
- Stripe for payments
- (Future) Google My Business API

### 8.2. Data storage & privacy
- PostgreSQL for structured data
- All data stored privately per business
- Hosted on user-owned VPS via Docker containers

### 8.3. Scalability & performance
- Node.js microservices with Docker containers
- PWA optimized frontend in Vue.js
- Flutter mobile app planned later
- CI/CD with GitHub for updates
- Designed for 10–100 agent operations per instance

### 8.4. Potential challenges
- Twilio regional restrictions and TURN server fallback
- Manual GMB onboarding delays
- Offline data sync edge cases
- Multi-instance update management

## 9. Milestones & sequencing
### 9.1. Project estimate
- Solo Developer + AI Agent

### 9.2. Team size & composition
- Solo Developer using LLM-based AI Co-pilot

### 9.3. Suggested phases
- **Phase 1**: Core setup and architecture bootstrapping
  - Monorepo, Docker, CI/CD, base UI, auth
- **Phase 2**: Inbound call handling & CRM flow
  - Phone UI, CRM, Twilio integration
- **Phase 3**: Service request & invoice system
  - Customer flow, invoice creation, payment
- **Phase 4**: Admin onboarding and KB config
  - Super admin setup, service config, invites
- **Phase 5**: Notifications, payouts & reporting
  - Firebase, agent performance, payouts
- **Phase 6**: Final PWA polish and deployment
  - Offline sync, deployment scripts

## 10. User stories
### 10.1. Login with Role-Based Dashboard
- **ID**: US-001
- **Description**: As a user, I want to log in and be redirected to a dashboard based on my role.
- **Acceptance criteria**:
  - User is authenticated via email/password or social login.
  - User is redirected to the correct dashboard based on role.

### 10.2. CSR Answers Incoming Call
- **ID**: US-002
- **Description**: As a CSR, I want to receive and answer browser-based calls using the Phone UI.
- **Acceptance criteria**:
  - Incoming call rings via Twilio on the browser UI.
  - CSR can accept or reject the call.
  - Call logs are created after each call ends.

### 10.3. CSR Dispositions Call
- **ID**: US-003
- **Description**: As a CSR, I want to disposition a call as sale, lead, or callback.
- **Acceptance criteria**:
  - Call disposition options are available post-call.
  - Each disposition updates the CRM with status and notes.
  - Callbacks are queued and visible in the callback queue.

### 10.4. Customer Requests Service Without Call
- **ID**: US-004
- **Description**: As a customer, I want to submit a service request via form without calling.
- **Acceptance criteria**:
  - Customer fills out service request form with contact and issue details.
  - Request is logged and visible to the assigned agent.
  - Customer receives confirmation notification.

### 10.5. On-Site Agent Accepts Service Request
- **ID**: US-005
- **Description**: As an on-site agent, I want to accept a job and schedule a visit.
- **Acceptance criteria**:
  - Agent sees incoming job request.
  - Agent sets appointment date and time.
  - Customer receives confirmation.

### 10.6. Agent Tracks Service Time
- **ID**: US-006
- **Description**: As an agent, I want to start and stop a timer to track time spent on service.
- **Acceptance criteria**:
  - Timer starts/stops manually.
  - Time is stored and used for billing.

### 10.7. Agent Raises Invoice
- **ID**: US-007
- **Description**: As an agent, I want to create an invoice linked to the completed service.
- **Acceptance criteria**:
  - Invoice supports hourly/fixed/subscription models.
  - Stripe link is generated.
  - Payment updates status.

### 10.8. Super Admin Onboards Business
- **ID**: US-008
- **Description**: As a super admin, I want to onboard a business with custom services and pricing.
- **Acceptance criteria**:
  - Admin fills out company info and services.
  - Can invite Remote Site Admin.
  - Instance is ready post setup.

### 10.9. Team Leader Monitors Agent KPIs
- **ID**: US-009
- **Description**: As a TL, I want to view call and sales metrics per agent.
- **Acceptance criteria**:
  - Dashboard shows KPIs.
  - TL can filter by date.
  - No edit access, view only.

### 10.10. System Sends Push Notifications
- **ID**: US-010
- **Description**: As an agent, I want to receive push notifications for new jobs or callbacks.
- **Acceptance criteria**:
  - Firebase delivers real-time messages.
  - Includes context and CTA.
  - Can dismiss or snooze.

### 10.11. Agent Gets Paid Commission
- **ID**: US-011
- **Description**: As an agent, I want to see my payout details based on completed invoices.
- **Acceptance criteria**:
  - Commission shown per invoice.
  - Payout method stored.
  - Admin can approve payment.

### 10.12. Secure Access and Session Handling
- **ID**: US-012
- **Description**: As a user, I want secure session handling and automatic logout after inactivity.
- **Acceptance criteria**:
  - Token-based auth with expiration.
  - Auto logout after inactivity.
  - Role-based route protection.
