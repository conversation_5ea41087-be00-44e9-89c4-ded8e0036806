/**
 * Wattatech Enterprise Platform - Interactive Mockup System
 * Main JavaScript file for mockup demonstrations and interactions
 */

class WattatechMockup {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupMobileFrame();
        this.loadSampleData();
    }

    setupEventListeners() {
        // Mobile menu toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-mobile-menu-toggle]')) {
                this.toggleMobileMenu();
            }
        });

        // Modal controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-modal-open]')) {
                const modalId = e.target.getAttribute('data-modal-open');
                this.openModal(modalId);
            }
            if (e.target.matches('[data-modal-close]')) {
                this.closeModal();
            }
        });

        // Tab switching
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-tab]')) {
                const tabId = e.target.getAttribute('data-tab');
                this.switchTab(tabId);
            }
        });

        // Form validation
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.validateField(e.target);
            }
        });

        // Dropdown toggles
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-dropdown-toggle]')) {
                const dropdownId = e.target.getAttribute('data-dropdown-toggle');
                this.toggleDropdown(dropdownId);
            }
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('[data-dropdown]')) {
                this.closeAllDropdowns();
            }
        });

        // Phone UI controls
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-phone-action]')) {
                const action = e.target.getAttribute('data-phone-action');
                this.handlePhoneAction(action);
            }
        });
    }

    initializeComponents() {
        // Initialize tooltips
        this.initTooltips();
        
        // Initialize data tables
        this.initDataTables();
        
        // Initialize charts (placeholders)
        this.initCharts();
        
        // Initialize notifications
        this.initNotifications();
    }

    setupMobileFrame() {
        const mobileFrames = document.querySelectorAll('.mobile-frame');
        mobileFrames.forEach(frame => {
            // Add status bar
            const statusBar = document.createElement('div');
            statusBar.className = 'mobile-status-bar';
            statusBar.innerHTML = `
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            `;
            
            const screen = frame.querySelector('.mobile-screen');
            if (screen) {
                screen.appendChild(statusBar);
            }
        });
    }

    toggleMobileMenu() {
        const menu = document.querySelector('[data-mobile-menu]');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        const modals = document.querySelectorAll('[data-modal]');
        modals.forEach(modal => {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        });
        document.body.style.overflow = '';
    }

    switchTab(tabId) {
        // Hide all tab contents
        const tabContents = document.querySelectorAll('[data-tab-content]');
        tabContents.forEach(content => {
            content.classList.add('hidden');
        });

        // Remove active state from all tabs
        const tabs = document.querySelectorAll('[data-tab]');
        tabs.forEach(tab => {
            tab.classList.remove('border-primary-500', 'text-primary-500');
            tab.classList.add('border-transparent', 'text-gray-400');
        });

        // Show selected tab content
        const selectedContent = document.querySelector(`[data-tab-content="${tabId}"]`);
        if (selectedContent) {
            selectedContent.classList.remove('hidden');
        }

        // Activate selected tab
        const selectedTab = document.querySelector(`[data-tab="${tabId}"]`);
        if (selectedTab) {
            selectedTab.classList.add('border-primary-500', 'text-primary-500');
            selectedTab.classList.remove('border-transparent', 'text-gray-400');
        }
    }

    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');
        
        let isValid = true;
        let errorMessage = '';

        // Required validation
        if (required && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        // Email validation
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid email address';
            }
        }

        // Phone validation
        if (type === 'tel' && value) {
            const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                errorMessage = 'Please enter a valid phone number';
            }
        }

        this.updateFieldValidation(field, isValid, errorMessage);
    }

    updateFieldValidation(field, isValid, errorMessage) {
        const errorElement = field.parentNode.querySelector('.field-error');
        
        if (isValid) {
            field.classList.remove('border-red-500');
            field.classList.add('border-gray-700');
            if (errorElement) {
                errorElement.remove();
            }
        } else {
            field.classList.add('border-red-500');
            field.classList.remove('border-gray-700');
            
            if (!errorElement) {
                const error = document.createElement('div');
                error.className = 'field-error text-red-400 text-sm mt-1';
                error.textContent = errorMessage;
                field.parentNode.appendChild(error);
            } else {
                errorElement.textContent = errorMessage;
            }
        }
    }

    toggleDropdown(dropdownId) {
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.classList.toggle('hidden');
        }
    }

    closeAllDropdowns() {
        const dropdowns = document.querySelectorAll('[data-dropdown-menu]');
        dropdowns.forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }

    handlePhoneAction(action) {
        console.log(`Phone action: ${action}`);
        
        switch (action) {
            case 'answer':
                this.simulateCallAnswer();
                break;
            case 'hangup':
                this.simulateCallHangup();
                break;
            case 'hold':
                this.simulateCallHold();
                break;
            case 'mute':
                this.simulateCallMute();
                break;
            case 'transfer':
                this.simulateCallTransfer();
                break;
        }
    }

    simulateCallAnswer() {
        const phoneUI = document.querySelector('.phone-ui');
        if (phoneUI) {
            phoneUI.classList.add('call-active');
            this.showNotification('Call answered', 'success');
        }
    }

    simulateCallHangup() {
        const phoneUI = document.querySelector('.phone-ui');
        if (phoneUI) {
            phoneUI.classList.remove('call-active');
            this.showNotification('Call ended', 'info');
        }
    }

    simulateCallHold() {
        this.showNotification('Call on hold', 'warning');
    }

    simulateCallMute() {
        this.showNotification('Call muted', 'info');
    }

    simulateCallTransfer() {
        this.showNotification('Transferring call...', 'info');
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${this.getNotificationClass(type)}`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${this.getNotificationIcon(type)} mr-2"></i>
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentNode.parentNode.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    getNotificationClass(type) {
        const classes = {
            success: 'bg-green-600',
            error: 'bg-red-600',
            warning: 'bg-yellow-600',
            info: 'bg-blue-600'
        };
        return classes[type] || classes.info;
    }

    getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    initTooltips() {
        // Simple tooltip implementation
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip absolute bg-gray-800 text-white px-2 py-1 rounded text-sm z-50';
                tooltip.textContent = e.target.getAttribute('data-tooltip');
                document.body.appendChild(tooltip);
                
                const rect = e.target.getBoundingClientRect();
                tooltip.style.left = rect.left + 'px';
                tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
            });
            
            element.addEventListener('mouseleave', () => {
                const tooltip = document.querySelector('.tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            });
        });
    }

    initDataTables() {
        // Simple data table sorting
        const tables = document.querySelectorAll('[data-table]');
        tables.forEach(table => {
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header.getAttribute('data-sort'));
                });
            });
        });
    }

    sortTable(table, column) {
        // Simple table sorting implementation
        console.log(`Sorting table by ${column}`);
        this.showNotification(`Table sorted by ${column}`, 'info');
    }

    initCharts() {
        // Placeholder for chart initialization
        const chartElements = document.querySelectorAll('[data-chart]');
        chartElements.forEach(element => {
            element.innerHTML = '<div class="flex items-center justify-center h-full text-gray-400"><i class="fas fa-chart-bar text-4xl"></i></div>';
        });
    }

    initNotifications() {
        // Initialize notification system
        console.log('Notification system initialized');
    }

    loadSampleData() {
        // Load sample data for demonstrations
        this.sampleData = {
            customers: [
                { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '+1234567890' },
                { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '+1234567891' }
            ],
            calls: [
                { id: 1, customer: 'John Doe', duration: '5:23', status: 'completed' },
                { id: 2, customer: 'Jane Smith', duration: '2:15', status: 'active' }
            ]
        };
    }
}

// Initialize the mockup system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WattatechMockup();
});

// Utility functions
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(new Date(date));
}
