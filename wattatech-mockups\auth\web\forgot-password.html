<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Wattatech Enterprise Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-950 text-white font-sans min-h-screen">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-key text-white text-2xl"></i>
                </div>
                <h2 class="text-3xl font-bold text-white">Forgot your password?</h2>
                <p class="mt-2 text-gray-400">No worries, we'll send you reset instructions.</p>
            </div>

            <div class="bg-gray-900 border border-gray-800 rounded-lg px-6 py-8 shadow-xl">
                <form class="space-y-6" id="forgotPasswordForm">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                            Email address
                        </label>
                        <div class="relative">
                            <input 
                                id="email" 
                                name="email" 
                                type="email" 
                                autocomplete="email" 
                                required 
                                class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                placeholder="Enter your email address"
                            >
                            <i class="fas fa-envelope absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        </div>
                    </div>

                    <div>
                        <button 
                            type="submit" 
                            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-gray-900 transition-colors"
                        >
                            <span class="flex items-center">
                                <i class="fas fa-paper-plane mr-2"></i>
                                Send Reset Instructions
                            </span>
                        </button>
                    </div>
                </form>

                <div class="mt-6 text-center">
                    <a href="login.html" class="text-sm text-primary-400 hover:text-primary-300 font-medium transition-colors flex items-center justify-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to sign in
                    </a>
                </div>
            </div>

            <!-- Success State (Hidden by default) -->
            <div id="successMessage" class="hidden bg-green-900/20 border border-green-700 rounded-lg px-6 py-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-white text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">Check your email</h3>
                    <p class="text-gray-300 text-sm mb-4">
                        We sent a password reset link to <span id="emailSent" class="font-medium"></span>
                    </p>
                    <p class="text-gray-400 text-xs">
                        Didn't receive the email? Check your spam folder or 
                        <button onclick="resendEmail()" class="text-primary-400 hover:text-primary-300 underline">
                            click to resend
                        </button>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const submitButton = e.target.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Sending...';
            submitButton.disabled = true;
            
            setTimeout(() => {
                // Hide form and show success message
                document.querySelector('.bg-gray-900').style.display = 'none';
                document.getElementById('successMessage').classList.remove('hidden');
                document.getElementById('emailSent').textContent = email;
            }, 2000);
        });

        function resendEmail() {
            const button = event.target;
            const originalText = button.textContent;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Sending...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = 'Email sent!';
                setTimeout(() => {
                    button.textContent = originalText;
                    button.disabled = false;
                }, 2000);
            }, 1500);
        }
    </script>
</body>
</html>
