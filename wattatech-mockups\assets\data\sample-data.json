{"users": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "role": "super_admin", "avatar": "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150", "status": "active", "lastLogin": "2024-01-15T10:30:00Z"}, {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "role": "site_admin", "avatar": "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150", "status": "active", "lastLogin": "2024-01-15T09:15:00Z"}, {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "role": "team_leader", "avatar": "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150", "status": "active", "lastLogin": "2024-01-15T08:45:00Z"}, {"id": 4, "name": "<PERSON>", "email": "<EMAIL>", "role": "csr", "avatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150", "status": "available", "lastLogin": "2024-01-15T08:00:00Z"}, {"id": 5, "name": "<PERSON>", "email": "<EMAIL>", "role": "onsite_owner", "avatar": "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150", "status": "active", "lastLogin": "2024-01-15T07:30:00Z"}, {"id": 6, "name": "<PERSON>", "email": "<EMAIL>", "role": "onsite_agent", "avatar": "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150", "status": "on_job", "lastLogin": "2024-01-15T06:00:00Z"}], "customers": [{"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+****************", "address": "123 Main St, Anytown, ST 12345", "status": "active", "lifetimeValue": 2450.0, "lastContact": "2024-01-14T15:30:00Z", "source": "Google My Business"}, {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+****************", "address": "456 Oak Ave, Somewhere, ST 23456", "status": "active", "lifetimeValue": 1875.5, "lastContact": "2024-01-13T11:15:00Z", "source": "Referral"}, {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "phone": "+****************", "address": "789 Pine Rd, Elsewhere, ST 34567", "status": "lead", "lifetimeValue": 0.0, "lastContact": "2024-01-15T09:45:00Z", "source": "Website"}], "calls": [{"id": 1, "customerId": 1, "customerName": "<PERSON>", "agentId": 4, "agentName": "<PERSON>", "direction": "inbound", "status": "completed", "duration": 323, "startTime": "2024-01-15T10:15:00Z", "endTime": "2024-01-15T10:20:23Z", "disposition": "sale", "notes": "Customer requested HVAC maintenance. Scheduled for tomorrow at 2 PM."}, {"id": 2, "customerId": 2, "customerName": "<PERSON>", "agentId": 4, "agentName": "<PERSON>", "direction": "outbound", "status": "completed", "duration": 156, "startTime": "2024-01-15T09:30:00Z", "endTime": "2024-01-15T09:32:36Z", "disposition": "callback", "notes": "Follow-up call scheduled for next week."}, {"id": 3, "customerId": 3, "customerName": "<PERSON>", "agentId": 4, "agentName": "<PERSON>", "direction": "inbound", "status": "active", "duration": 0, "startTime": "2024-01-15T10:45:00Z", "endTime": null, "disposition": null, "notes": ""}], "services": [{"id": 1, "name": "HVAC Maintenance", "category": "Maintenance", "description": "Complete HVAC system inspection and maintenance", "pricingModel": "fixed", "basePrice": 150.0, "duration": 120, "isActive": true}, {"id": 2, "name": "Plumbing Repair", "category": "Repair", "description": "General plumbing repair services", "pricingModel": "hourly", "hourlyRate": 85.0, "duration": null, "isActive": true}, {"id": 3, "name": "Electrical Installation", "category": "Installation", "description": "Electrical fixture and wiring installation", "pricingModel": "hourly", "hourlyRate": 95.0, "duration": null, "isActive": true}], "serviceRequests": [{"id": 1, "customerId": 1, "customerName": "<PERSON>", "serviceId": 1, "serviceName": "HVAC Maintenance", "agentId": 6, "agentName": "<PERSON>", "title": "Annual HVAC Maintenance", "description": "Need annual maintenance for central air system", "status": "scheduled", "priority": "medium", "scheduledAt": "2024-01-16T14:00:00Z", "estimatedDuration": 120, "createdAt": "2024-01-15T10:20:00Z"}, {"id": 2, "customerId": 2, "customerName": "<PERSON>", "serviceId": 2, "serviceName": "Plumbing Repair", "agentId": null, "agentName": null, "title": "Kitchen Sink Leak", "description": "Kitchen sink has been leaking under the cabinet", "status": "pending", "priority": "high", "scheduledAt": null, "estimatedDuration": 60, "createdAt": "2024-01-15T08:30:00Z"}], "invoices": [{"id": 1, "invoiceNumber": "INV-2024-001", "customerId": 1, "customerName": "<PERSON>", "agentId": 6, "agentName": "<PERSON>", "serviceRequestId": 1, "subtotal": 150.0, "taxAmount": 12.0, "totalAmount": 162.0, "status": "paid", "paidAt": "2024-01-14T16:30:00Z", "dueDate": "2024-01-28", "createdAt": "2024-01-14T15:00:00Z", "items": [{"description": "HVAC Maintenance Service", "quantity": 1, "unitPrice": 150.0, "totalPrice": 150.0}]}, {"id": 2, "invoiceNumber": "INV-2024-002", "customerId": 2, "customerName": "<PERSON>", "agentId": 6, "agentName": "<PERSON>", "serviceRequestId": null, "subtotal": 255.0, "taxAmount": 20.4, "totalAmount": 275.4, "status": "pending", "paidAt": null, "dueDate": "2024-01-30", "createdAt": "2024-01-15T11:00:00Z", "items": [{"description": "Plumbing Repair - 3 hours", "quantity": 3, "unitPrice": 85.0, "totalPrice": 255.0}]}], "analytics": {"callMetrics": {"totalCalls": 156, "answeredCalls": 142, "missedCalls": 14, "averageHandleTime": 245, "conversionRate": 0.68}, "serviceMetrics": {"totalServices": 89, "completedServices": 76, "pendingServices": 13, "averageRating": 4.7, "onTimeCompletion": 0.92}, "financialMetrics": {"totalRevenue": 45670.0, "monthlyRevenue": 12450.0, "averageInvoiceValue": 285.5, "paymentSuccessRate": 0.94}, "agentMetrics": {"totalAgents": 12, "activeAgents": 8, "averagePerformanceScore": 87.5, "topPerformer": "<PERSON>"}}, "notifications": [{"id": 1, "type": "call", "title": "Incoming Call", "message": "New call from <PERSON>", "timestamp": "2024-01-15T10:45:00Z", "read": false, "priority": "high"}, {"id": 2, "type": "service", "title": "Service Completed", "message": "HVAC maintenance completed for <PERSON>", "timestamp": "2024-01-15T09:30:00Z", "read": true, "priority": "medium"}, {"id": 3, "type": "payment", "title": "Payment Received", "message": "Payment of $162.00 received from <PERSON>", "timestamp": "2024-01-14T16:30:00Z", "read": true, "priority": "low"}]}