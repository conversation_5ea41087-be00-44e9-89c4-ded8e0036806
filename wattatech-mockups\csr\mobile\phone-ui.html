<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone UI - Wattatech CSR Mobile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
    <style>
        .dialer-button {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: #374151;
            border: 1px solid #4b5563;
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            transition: all 0.2s ease;
            font-size: 18px;
        }
        .dialer-button:active {
            transform: scale(0.95);
            background: #4b5563;
        }
        .dialer-button .letters {
            font-size: 10px;
            margin-top: 2px;
            opacity: 0.7;
        }
        .call-timer {
            font-variant-numeric: tabular-nums;
        }
        @keyframes pulse-ring {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(1.3); opacity: 0; }
        }
        .pulse-ring {
            animation: pulse-ring 1.5s infinite;
        }
    </style>
</head>
<body class="bg-gray-950 text-white font-sans">
    <div class="flex justify-center items-center min-h-screen p-4">
        <div class="mobile-frame">
            <div class="mobile-screen">
                <!-- Mobile Status Bar -->
                <div class="mobile-status-bar">
                    <span>9:41</span>
                    <span class="flex items-center space-x-1">
                        <i class="fas fa-signal text-xs"></i>
                        <i class="fas fa-wifi text-xs"></i>
                        <i class="fas fa-battery-three-quarters text-xs"></i>
                    </span>
                </div>

                <!-- Mobile Content -->
                <div class="mobile-content bg-gray-900 relative">
                    <!-- Header -->
                    <div class="bg-gray-800 px-4 py-3 border-b border-gray-700">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <button onclick="goBack()" class="text-gray-400 hover:text-white mr-3">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <h1 class="text-lg font-bold text-white">Phone</h1>
                            </div>
                            <div class="flex items-center">
                                <div id="mobilePhoneStatus" class="flex items-center">
                                    <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-gray-300">Ready</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Phone Interface -->
                    <div class="p-4">
                        <!-- Idle State -->
                        <div id="mobilePhoneIdle" class="text-center">
                            <div class="w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-6 mt-8">
                                <i class="fas fa-phone text-white text-3xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold text-white mb-2">Ready to take calls</h2>
                            <p class="text-gray-400 mb-8">Tap to open dialer or wait for incoming calls</p>
                            
                            <div class="space-y-4">
                                <button onclick="showMobileDialer()" class="w-full py-4 bg-gray-800 hover:bg-gray-700 text-white rounded-xl transition-colors">
                                    <i class="fas fa-th mr-3"></i>
                                    Open Dialer
                                </button>
                                <button onclick="simulateMobileIncoming()" class="w-full py-4 bg-green-600 hover:bg-green-700 text-white rounded-xl transition-colors">
                                    <i class="fas fa-phone mr-3"></i>
                                    Simulate Incoming Call
                                </button>
                            </div>
                        </div>

                        <!-- Dialer State -->
                        <div id="mobilePhoneDialer" class="hidden">
                            <div class="mb-6">
                                <input type="text" id="mobileDialerInput" class="w-full px-4 py-4 bg-gray-800 border border-gray-700 rounded-xl text-white text-center text-2xl font-mono" placeholder="Enter number" readonly>
                            </div>
                            
                            <div class="grid grid-cols-3 gap-4 mb-6">
                                <button onclick="mobileDialNumber('1')" class="dialer-button mx-auto">1</button>
                                <button onclick="mobileDialNumber('2')" class="dialer-button mx-auto">2<div class="letters">ABC</div></button>
                                <button onclick="mobileDialNumber('3')" class="dialer-button mx-auto">3<div class="letters">DEF</div></button>
                                <button onclick="mobileDialNumber('4')" class="dialer-button mx-auto">4<div class="letters">GHI</div></button>
                                <button onclick="mobileDialNumber('5')" class="dialer-button mx-auto">5<div class="letters">JKL</div></button>
                                <button onclick="mobileDialNumber('6')" class="dialer-button mx-auto">6<div class="letters">MNO</div></button>
                                <button onclick="mobileDialNumber('7')" class="dialer-button mx-auto">7<div class="letters">PQRS</div></button>
                                <button onclick="mobileDialNumber('8')" class="dialer-button mx-auto">8<div class="letters">TUV</div></button>
                                <button onclick="mobileDialNumber('9')" class="dialer-button mx-auto">9<div class="letters">WXYZ</div></button>
                                <button onclick="mobileDialNumber('*')" class="dialer-button mx-auto">*</button>
                                <button onclick="mobileDialNumber('0')" class="dialer-button mx-auto">0<div class="letters">+</div></button>
                                <button onclick="mobileDialNumber('#')" class="dialer-button mx-auto">#</button>
                            </div>
                            
                            <div class="flex justify-center space-x-6">
                                <button onclick="mobileClearDialer()" class="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-backspace text-xl"></i>
                                </button>
                                <button onclick="mobileMakeCall()" class="w-20 h-20 bg-green-600 hover:bg-green-700 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-phone text-2xl"></i>
                                </button>
                                <button onclick="hideMobileDialer()" class="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Incoming Call State -->
                        <div id="mobilePhoneIncoming" class="hidden text-center">
                            <div class="mt-16 mb-8">
                                <div class="relative inline-block">
                                    <div class="w-32 h-32 bg-green-600 rounded-full flex items-center justify-center pulse-ring absolute inset-0"></div>
                                    <div class="w-32 h-32 bg-green-600 rounded-full flex items-center justify-center relative">
                                        <i class="fas fa-phone text-white text-4xl"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <h2 class="text-2xl font-bold text-white mb-2" id="mobileIncomingCallerName">William Davis</h2>
                            <p class="text-lg text-gray-400 mb-2" id="mobileIncomingCallerNumber">+****************</p>
                            <p class="text-sm text-gray-500 mb-12">Incoming Call</p>
                            
                            <div class="flex justify-center space-x-12">
                                <button onclick="mobileDeclineCall()" class="w-20 h-20 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-phone-slash text-2xl"></i>
                                </button>
                                <button onclick="mobileAnswerCall()" class="w-20 h-20 bg-green-600 hover:bg-green-700 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-phone text-2xl"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Active Call State -->
                        <div id="mobilePhoneActive" class="hidden text-center">
                            <div class="mt-12 mb-8">
                                <div class="w-32 h-32 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-phone text-white text-4xl"></i>
                                </div>
                                <h2 class="text-xl font-bold text-white mb-1" id="mobileActiveCallerName">William Davis</h2>
                                <p class="text-gray-400 mb-2" id="mobileActiveCallerNumber">+****************</p>
                                <p class="text-lg text-gray-300 call-timer mb-8" id="mobileCallTimer">00:00</p>
                            </div>
                            
                            <div class="grid grid-cols-3 gap-6 mb-8">
                                <button onclick="mobileMuteCall()" id="mobileMuteBtn" class="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-microphone text-xl"></i>
                                </button>
                                <button onclick="mobileHoldCall()" id="mobileHoldBtn" class="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-pause text-xl"></i>
                                </button>
                                <button onclick="mobileTransferCall()" class="w-16 h-16 bg-gray-700 hover:bg-gray-600 rounded-full flex items-center justify-center text-white transition-colors">
                                    <i class="fas fa-share text-xl"></i>
                                </button>
                            </div>
                            
                            <button onclick="mobileEndCall()" class="w-20 h-20 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center text-white transition-colors mx-auto">
                                <i class="fas fa-phone-slash text-2xl"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Bottom Navigation -->
                    <div class="mobile-bottom-nav">
                        <div class="grid grid-cols-5 py-2">
                            <button onclick="goToDashboard()" class="p-3 text-center">
                                <i class="fas fa-home text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Dashboard</span>
                            </button>
                            <button class="p-3 text-center">
                                <i class="fas fa-phone text-primary-500 text-lg mb-1 block"></i>
                                <span class="text-xs text-primary-500">Phone</span>
                            </button>
                            <button onclick="goToCustomers()" class="p-3 text-center">
                                <i class="fas fa-users text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Customers</span>
                            </button>
                            <button onclick="goToLeads()" class="p-3 text-center">
                                <i class="fas fa-chart-line text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Leads</span>
                            </button>
                            <button onclick="goToProfile()" class="p-3 text-center">
                                <i class="fas fa-user text-gray-400 text-lg mb-1 block"></i>
                                <span class="text-xs text-gray-400">Profile</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Mobile Home Indicator -->
                <div class="mobile-home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        let mobileCallTimer = 0;
        let mobileCallInterval;
        let mobileIsMuted = false;
        let mobileIsOnHold = false;

        function updateMobilePhoneStatus(status, color) {
            const statusElement = document.getElementById('mobilePhoneStatus');
            const dot = statusElement.querySelector('.w-2');
            const text = statusElement.querySelector('span');
            
            dot.className = `w-2 h-2 rounded-full mr-2 bg-${color}-500`;
            text.textContent = status;
        }

        function showMobileState(stateId) {
            const states = ['mobilePhoneIdle', 'mobilePhoneDialer', 'mobilePhoneIncoming', 'mobilePhoneActive'];
            states.forEach(state => {
                document.getElementById(state).classList.add('hidden');
            });
            document.getElementById(stateId).classList.remove('hidden');
        }

        function showMobileDialer() {
            showMobileState('mobilePhoneDialer');
            updateMobilePhoneStatus('Dialing', 'blue');
        }

        function hideMobileDialer() {
            showMobileState('mobilePhoneIdle');
            updateMobilePhoneStatus('Ready', 'gray');
            mobileClearDialer();
        }

        function mobileDialNumber(number) {
            const input = document.getElementById('mobileDialerInput');
            input.value += number;
            
            // Haptic feedback simulation
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }

        function mobileClearDialer() {
            const input = document.getElementById('mobileDialerInput');
            if (input.value.length > 0) {
                input.value = input.value.slice(0, -1);
            }
        }

        function mobileMakeCall() {
            const number = document.getElementById('mobileDialerInput').value;
            if (number) {
                mobileAnswerCall();
            }
        }

        function simulateMobileIncoming() {
            showMobileState('mobilePhoneIncoming');
            updateMobilePhoneStatus('Ringing', 'yellow');
            
            // Vibration pattern for incoming call
            if (navigator.vibrate) {
                navigator.vibrate([500, 200, 500, 200, 500]);
            }
        }

        function mobileAnswerCall() {
            showMobileState('mobilePhoneActive');
            updateMobilePhoneStatus('Active', 'green');
            
            // Start call timer
            mobileCallTimer = 0;
            mobileCallInterval = setInterval(() => {
                mobileCallTimer++;
                const minutes = Math.floor(mobileCallTimer / 60);
                const seconds = mobileCallTimer % 60;
                document.getElementById('mobileCallTimer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function mobileDeclineCall() {
            mobileResetPhone();
        }

        function mobileEndCall() {
            clearInterval(mobileCallInterval);
            mobileResetPhone();
            
            // Show call disposition after a brief delay
            setTimeout(() => {
                if (confirm('Call ended. Would you like to add a disposition?')) {
                    window.location.href = 'call-disposition.html';
                }
            }, 500);
        }

        function mobileMuteCall() {
            mobileIsMuted = !mobileIsMuted;
            const muteBtn = document.getElementById('mobileMuteBtn');
            
            if (mobileIsMuted) {
                muteBtn.classList.add('bg-red-600');
                muteBtn.classList.remove('bg-gray-700');
                muteBtn.innerHTML = '<i class="fas fa-microphone-slash text-xl"></i>';
                updateMobilePhoneStatus('Muted', 'red');
            } else {
                muteBtn.classList.remove('bg-red-600');
                muteBtn.classList.add('bg-gray-700');
                muteBtn.innerHTML = '<i class="fas fa-microphone text-xl"></i>';
                updateMobilePhoneStatus('Active', 'green');
            }
            
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(100);
            }
        }

        function mobileHoldCall() {
            mobileIsOnHold = !mobileIsOnHold;
            const holdBtn = document.getElementById('mobileHoldBtn');
            
            if (mobileIsOnHold) {
                holdBtn.classList.add('bg-yellow-600');
                holdBtn.classList.remove('bg-gray-700');
                holdBtn.innerHTML = '<i class="fas fa-play text-xl"></i>';
                updateMobilePhoneStatus('On Hold', 'yellow');
            } else {
                holdBtn.classList.remove('bg-yellow-600');
                holdBtn.classList.add('bg-gray-700');
                holdBtn.innerHTML = '<i class="fas fa-pause text-xl"></i>';
                updateMobilePhoneStatus('Active', 'green');
            }
            
            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(100);
            }
        }

        function mobileTransferCall() {
            if (confirm('Transfer call to on-site agent?')) {
                mobileEndCall();
            }
        }

        function mobileResetPhone() {
            showMobileState('mobilePhoneIdle');
            updateMobilePhoneStatus('Ready', 'gray');
            clearInterval(mobileCallInterval);
            mobileCallTimer = 0;
            mobileIsMuted = false;
            mobileIsOnHold = false;
            
            // Reset button states
            const muteBtn = document.getElementById('mobileMuteBtn');
            const holdBtn = document.getElementById('mobileHoldBtn');
            if (muteBtn) {
                muteBtn.classList.remove('bg-red-600');
                muteBtn.classList.add('bg-gray-700');
                muteBtn.innerHTML = '<i class="fas fa-microphone text-xl"></i>';
            }
            if (holdBtn) {
                holdBtn.classList.remove('bg-yellow-600');
                holdBtn.classList.add('bg-gray-700');
                holdBtn.innerHTML = '<i class="fas fa-pause text-xl"></i>';
            }
            
            mobileClearDialer();
        }

        // Navigation functions
        function goBack() {
            window.location.href = 'dashboard.html';
        }

        function goToDashboard() {
            window.location.href = 'dashboard.html';
        }

        function goToCustomers() {
            window.location.href = 'customers/list.html';
        }

        function goToLeads() {
            window.location.href = 'leads/pipeline.html';
        }

        function goToProfile() {
            window.location.href = 'profile/view.html';
        }

        // Touch interactions
        document.addEventListener('touchstart', function(e) {
            if (e.target.matches('button, .dialer-button')) {
                e.target.style.transform = 'scale(0.95)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.matches('button, .dialer-button')) {
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 100);
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            mobileResetPhone();
        });
    </script>
</body>
</html>
