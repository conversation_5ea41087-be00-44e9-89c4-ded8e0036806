<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Wattatech Enterprise Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-950 text-white font-sans min-h-screen">
    <div class="min-h-screen flex">
        <!-- Left Side - Branding -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900 relative overflow-hidden">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative z-10 flex flex-col justify-center px-12 py-12">
                <div class="max-w-md">
                    <div class="mb-8">
                        <h1 class="text-4xl font-bold text-white mb-2">Wattatech</h1>
                        <p class="text-primary-100 text-lg">Enterprise Service Platform</p>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-phone text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-1">Unified Communication</h3>
                                <p class="text-primary-100 text-sm">Handle all customer calls, SMS, and service requests in one platform</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-users text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-1">Team Management</h3>
                                <p class="text-primary-100 text-sm">Manage remote and on-site agents with real-time monitoring</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-line text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-1">Analytics & Insights</h3>
                                <p class="text-primary-100 text-sm">Track performance, revenue, and customer satisfaction</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Decorative Elements -->
            <div class="absolute top-0 right-0 w-64 h-64 bg-primary-500/10 rounded-full -translate-y-32 translate-x-32"></div>
            <div class="absolute bottom-0 left-0 w-48 h-48 bg-primary-400/10 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="flex-1 flex flex-col justify-center px-6 py-12 lg:px-8">
            <div class="sm:mx-auto sm:w-full sm:max-w-md">
                <!-- Mobile Logo -->
                <div class="lg:hidden text-center mb-8">
                    <h1 class="text-3xl font-bold text-white mb-2">Wattatech</h1>
                    <p class="text-gray-400">Enterprise Service Platform</p>
                </div>

                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white">Welcome back</h2>
                    <p class="mt-2 text-gray-400">Sign in to your account to continue</p>
                </div>

                <div class="bg-gray-900 border border-gray-800 rounded-lg px-6 py-8 shadow-xl">
                    <form class="space-y-6" id="loginForm">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                                Email address
                            </label>
                            <div class="relative">
                                <input 
                                    id="email" 
                                    name="email" 
                                    type="email" 
                                    autocomplete="email" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="Enter your email"
                                >
                                <i class="fas fa-envelope absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                                Password
                            </label>
                            <div class="relative">
                                <input 
                                    id="password" 
                                    name="password" 
                                    type="password" 
                                    autocomplete="current-password" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="Enter your password"
                                >
                                <button 
                                    type="button" 
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                                    onclick="togglePassword()"
                                >
                                    <i class="fas fa-eye" id="passwordToggle"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input 
                                    id="remember-me" 
                                    name="remember-me" 
                                    type="checkbox" 
                                    class="h-4 w-4 text-primary-600 bg-gray-800 border-gray-700 rounded focus:ring-primary-500 focus:ring-2"
                                >
                                <label for="remember-me" class="ml-2 block text-sm text-gray-300">
                                    Remember me
                                </label>
                            </div>

                            <div class="text-sm">
                                <a href="forgot-password.html" class="text-primary-400 hover:text-primary-300 transition-colors">
                                    Forgot your password?
                                </a>
                            </div>
                        </div>

                        <div>
                            <button 
                                type="submit" 
                                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-gray-900 transition-colors"
                            >
                                <span class="flex items-center">
                                    <i class="fas fa-sign-in-alt mr-2"></i>
                                    Sign in
                                </span>
                            </button>
                        </div>

                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-700"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-gray-900 text-gray-400">Or continue with</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <button 
                                type="button" 
                                class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-700 rounded-lg shadow-sm bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-colors"
                            >
                                <i class="fab fa-google text-red-400 mr-2"></i>
                                Google
                            </button>
                            <button 
                                type="button" 
                                class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-700 rounded-lg shadow-sm bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-colors"
                            >
                                <i class="fab fa-microsoft text-blue-400 mr-2"></i>
                                Microsoft
                            </button>
                        </div>
                    </form>

                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-400">
                            Don't have an account?
                            <a href="register.html" class="text-primary-400 hover:text-primary-300 font-medium transition-colors">
                                Sign up here
                            </a>
                        </p>
                    </div>
                </div>

                <!-- Role Selection Demo -->
                <div class="mt-6 p-4 bg-gray-900/50 border border-gray-800 rounded-lg">
                    <p class="text-xs text-gray-400 mb-3 text-center">Demo Login Options:</p>
                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <button onclick="demoLogin('super_admin')" class="p-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 transition-colors">
                            <i class="fas fa-crown mr-1"></i> Super Admin
                        </button>
                        <button onclick="demoLogin('site_admin')" class="p-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 transition-colors">
                            <i class="fas fa-building mr-1"></i> Site Admin
                        </button>
                        <button onclick="demoLogin('team_leader')" class="p-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 transition-colors">
                            <i class="fas fa-users mr-1"></i> Team Leader
                        </button>
                        <button onclick="demoLogin('csr')" class="p-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 transition-colors">
                            <i class="fas fa-headset mr-1"></i> CSR
                        </button>
                        <button onclick="demoLogin('onsite_owner')" class="p-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 transition-colors">
                            <i class="fas fa-map-marked-alt mr-1"></i> Owner
                        </button>
                        <button onclick="demoLogin('onsite_agent')" class="p-2 bg-gray-800 hover:bg-gray-700 rounded text-gray-300 transition-colors">
                            <i class="fas fa-tools mr-1"></i> Agent
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordToggle = document.getElementById('passwordToggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.classList.remove('fa-eye');
                passwordToggle.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordToggle.classList.remove('fa-eye-slash');
                passwordToggle.classList.add('fa-eye');
            }
        }

        function demoLogin(role) {
            const roleRoutes = {
                'super_admin': '../../super-admin/web/dashboard.html',
                'site_admin': '../../site-admin/web/dashboard.html',
                'team_leader': '../../team-leader/web/dashboard.html',
                'csr': '../../csr/web/dashboard.html',
                'onsite_owner': '../../onsite-owner/web/dashboard.html',
                'onsite_agent': '../../onsite-agent/web/dashboard.html'
            };
            
            // Simulate login process
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Logging in...';
            button.disabled = true;
            
            setTimeout(() => {
                window.location.href = roleRoutes[role];
            }, 1500);
        }

        // Form submission handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (email && password) {
                // Simulate login
                const submitButton = e.target.querySelector('button[type="submit"]');
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Signing in...';
                submitButton.disabled = true;
                
                setTimeout(() => {
                    // Default to CSR dashboard for demo
                    window.location.href = '../../csr/web/dashboard.html';
                }, 2000);
            }
        });
    </script>
</body>
</html>
