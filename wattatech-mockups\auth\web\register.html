<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Wattatech Enterprise Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-950 text-white font-sans min-h-screen">
    <div class="min-h-screen flex">
        <!-- Left Side - Branding -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-900 relative overflow-hidden">
            <div class="absolute inset-0 bg-black/20"></div>
            <div class="relative z-10 flex flex-col justify-center px-12 py-12">
                <div class="max-w-md">
                    <div class="mb-8">
                        <h1 class="text-4xl font-bold text-white mb-2">Join Wattatech</h1>
                        <p class="text-primary-100 text-lg">Start managing your service business today</p>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-rocket text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-1">Quick Setup</h3>
                                <p class="text-primary-100 text-sm">Get your service business online in minutes</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-shield-alt text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-1">Enterprise Security</h3>
                                <p class="text-primary-100 text-sm">Bank-level security for your business data</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-headset text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-1">24/7 Support</h3>
                                <p class="text-primary-100 text-sm">Expert support whenever you need it</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Decorative Elements -->
            <div class="absolute top-0 right-0 w-64 h-64 bg-primary-500/10 rounded-full -translate-y-32 translate-x-32"></div>
            <div class="absolute bottom-0 left-0 w-48 h-48 bg-primary-400/10 rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        <!-- Right Side - Registration Form -->
        <div class="flex-1 flex flex-col justify-center px-6 py-12 lg:px-8">
            <div class="sm:mx-auto sm:w-full sm:max-w-md">
                <!-- Mobile Logo -->
                <div class="lg:hidden text-center mb-8">
                    <h1 class="text-3xl font-bold text-white mb-2">Wattatech</h1>
                    <p class="text-gray-400">Enterprise Service Platform</p>
                </div>

                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white">Create your account</h2>
                    <p class="mt-2 text-gray-400">Join thousands of service businesses</p>
                </div>

                <div class="bg-gray-900 border border-gray-800 rounded-lg px-6 py-8 shadow-xl">
                    <form class="space-y-6" id="registerForm">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-300 mb-2">
                                    First name
                                </label>
                                <input 
                                    id="firstName" 
                                    name="firstName" 
                                    type="text" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="John"
                                >
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium text-gray-300 mb-2">
                                    Last name
                                </label>
                                <input 
                                    id="lastName" 
                                    name="lastName" 
                                    type="text" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="Doe"
                                >
                            </div>
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                                Email address
                            </label>
                            <div class="relative">
                                <input 
                                    id="email" 
                                    name="email" 
                                    type="email" 
                                    autocomplete="email" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="<EMAIL>"
                                >
                                <i class="fas fa-envelope absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">
                                Phone number
                            </label>
                            <div class="relative">
                                <input 
                                    id="phone" 
                                    name="phone" 
                                    type="tel" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="+****************"
                                >
                                <i class="fas fa-phone absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div>
                            <label for="company" class="block text-sm font-medium text-gray-300 mb-2">
                                Company name
                            </label>
                            <div class="relative">
                                <input 
                                    id="company" 
                                    name="company" 
                                    type="text" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="Your Company Name"
                                >
                                <i class="fas fa-building absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                                Password
                            </label>
                            <div class="relative">
                                <input 
                                    id="password" 
                                    name="password" 
                                    type="password" 
                                    autocomplete="new-password" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="Create a strong password"
                                >
                                <button 
                                    type="button" 
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                                    onclick="togglePassword('password')"
                                >
                                    <i class="fas fa-eye" id="passwordToggle"></i>
                                </button>
                            </div>
                            <div class="mt-2">
                                <div class="flex items-center space-x-2 text-xs">
                                    <div class="flex-1 bg-gray-700 rounded-full h-1">
                                        <div class="bg-red-500 h-1 rounded-full w-1/4" id="passwordStrength"></div>
                                    </div>
                                    <span class="text-gray-400" id="passwordStrengthText">Weak</span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">
                                Confirm password
                            </label>
                            <div class="relative">
                                <input 
                                    id="confirmPassword" 
                                    name="confirmPassword" 
                                    type="password" 
                                    required 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors"
                                    placeholder="Confirm your password"
                                >
                                <button 
                                    type="button" 
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
                                    onclick="togglePassword('confirmPassword')"
                                >
                                    <i class="fas fa-eye" id="confirmPasswordToggle"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input 
                                    id="terms" 
                                    name="terms" 
                                    type="checkbox" 
                                    required
                                    class="h-4 w-4 text-primary-600 bg-gray-800 border-gray-700 rounded focus:ring-primary-500 focus:ring-2"
                                >
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="terms" class="text-gray-300">
                                    I agree to the 
                                    <a href="#" class="text-primary-400 hover:text-primary-300">Terms of Service</a> 
                                    and 
                                    <a href="#" class="text-primary-400 hover:text-primary-300">Privacy Policy</a>
                                </label>
                            </div>
                        </div>

                        <div>
                            <button 
                                type="submit" 
                                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-gray-900 transition-colors"
                            >
                                <span class="flex items-center">
                                    <i class="fas fa-user-plus mr-2"></i>
                                    Create Account
                                </span>
                            </button>
                        </div>

                        <div class="relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-700"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-gray-900 text-gray-400">Or sign up with</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <button 
                                type="button" 
                                class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-700 rounded-lg shadow-sm bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-colors"
                            >
                                <i class="fab fa-google text-red-400 mr-2"></i>
                                Google
                            </button>
                            <button 
                                type="button" 
                                class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-700 rounded-lg shadow-sm bg-gray-800 text-sm font-medium text-gray-300 hover:bg-gray-700 transition-colors"
                            >
                                <i class="fab fa-microsoft text-blue-400 mr-2"></i>
                                Microsoft
                            </button>
                        </div>
                    </form>

                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-400">
                            Already have an account?
                            <a href="login.html" class="text-primary-400 hover:text-primary-300 font-medium transition-colors">
                                Sign in here
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        function togglePassword(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const passwordToggle = document.getElementById(fieldId + 'Toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.classList.remove('fa-eye');
                passwordToggle.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordToggle.classList.remove('fa-eye-slash');
                passwordToggle.classList.add('fa-eye');
            }
        }

        // Password strength checker
        document.getElementById('password').addEventListener('input', function(e) {
            const password = e.target.value;
            const strengthBar = document.getElementById('passwordStrength');
            const strengthText = document.getElementById('passwordStrengthText');
            
            let strength = 0;
            let strengthLabel = 'Weak';
            let strengthColor = 'bg-red-500';
            let strengthWidth = 'w-1/4';
            
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    strengthLabel = 'Weak';
                    strengthColor = 'bg-red-500';
                    strengthWidth = 'w-1/4';
                    break;
                case 2:
                case 3:
                    strengthLabel = 'Fair';
                    strengthColor = 'bg-yellow-500';
                    strengthWidth = 'w-2/4';
                    break;
                case 4:
                    strengthLabel = 'Good';
                    strengthColor = 'bg-blue-500';
                    strengthWidth = 'w-3/4';
                    break;
                case 5:
                    strengthLabel = 'Strong';
                    strengthColor = 'bg-green-500';
                    strengthWidth = 'w-full';
                    break;
            }
            
            strengthBar.className = `${strengthColor} h-1 rounded-full ${strengthWidth} transition-all duration-300`;
            strengthText.textContent = strengthLabel;
        });

        // Form submission handler
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (password !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }
            
            const submitButton = e.target.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Creating Account...';
            submitButton.disabled = true;
            
            setTimeout(() => {
                // Redirect to email verification
                window.location.href = 'verify-email.html';
            }, 2000);
        });
    </script>
</body>
</html>
