# PRD: Wattatech PostgreSQL Database Schema

## 1. Product overview
### 1.1 Document title and version
- PRD: Wattatech PostgreSQL Database Schema
- Version: 1.0.0

### 1.2 Product summary
This PRD focuses on designing and implementing a comprehensive PostgreSQL database schema for the Wattatech platform. The database will support multi-tenant architecture, role-based access control, call management, customer relationship management, invoicing, agent tracking, and analytics. The schema will be optimized for performance, scalability, and data integrity while supporting complex business workflows and reporting requirements.

The database design will accommodate the microservices architecture with proper data isolation, referential integrity, and efficient query patterns for real-time operations and analytical workloads.

## 2. Goals
### 2.1 Business goals
- Design a scalable database schema that supports business growth
- Ensure data integrity and consistency across all business operations
- Enable efficient reporting and analytics for business intelligence
- Support multi-tenant architecture for different business instances
- Maintain audit trails for compliance and dispute resolution

### 2.2 Technical goals
- Optimize database performance for high-frequency operations
- Implement proper indexing strategies for fast query execution
- Design for horizontal scaling and read replica support
- Ensure data security with proper access controls and encryption
- Support efficient backup and disaster recovery procedures

### 2.3 Non-goals
- Application logic implementation (handled by backend services)
- User interface design and frontend considerations
- Third-party service integration details
- Infrastructure provisioning and database administration
- Real-time synchronization mechanisms (handled by application layer)

## 3. Database architecture
### 3.1 Core design principles
- **Normalization**: Third normal form (3NF) for transactional tables
- **Denormalization**: Strategic denormalization for reporting and analytics
- **Multi-tenancy**: Tenant isolation through schema design
- **Audit Trail**: Comprehensive change tracking for critical entities
- **Soft Deletes**: Logical deletion for data recovery and compliance

### 3.2 Schema organization
- **Core Schema**: User management, authentication, and system configuration
- **Business Schema**: Customers, services, invoicing, and business logic
- **Communication Schema**: Calls, messages, and interaction tracking
- **Analytics Schema**: Reporting tables, aggregations, and KPI calculations
- **Audit Schema**: Change logs, system events, and compliance data

### 3.3 Data types and conventions
- **Primary Keys**: UUID for distributed system compatibility
- **Timestamps**: UTC timezone with created_at and updated_at fields
- **Soft Deletes**: deleted_at timestamp field for logical deletion
- **JSON Fields**: Flexible data storage for configuration and metadata
- **Enums**: Database enums for status fields and categorical data

## 4. Core table specifications
### 4.1 User management tables
```sql
-- Users table for authentication and basic profile
users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  role user_role_enum NOT NULL,
  status user_status_enum DEFAULT 'active',
  email_verified_at TIMESTAMP,
  last_login_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP
);

-- Organizations for multi-tenant support
organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  domain VARCHAR(100) UNIQUE,
  settings JSONB DEFAULT '{}',
  subscription_plan VARCHAR(50),
  subscription_status VARCHAR(20),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- User-Organization relationships
user_organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  role organization_role_enum NOT NULL,
  permissions JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, organization_id)
);
```

### 4.2 Customer and CRM tables
```sql
-- Customers table
customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20) NOT NULL,
  address JSONB,
  customer_type customer_type_enum DEFAULT 'individual',
  lead_source VARCHAR(100),
  lead_status lead_status_enum DEFAULT 'new',
  lifetime_value DECIMAL(10,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP
);

-- Customer interactions and history
customer_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  interaction_type interaction_type_enum NOT NULL,
  subject VARCHAR(255),
  description TEXT,
  outcome VARCHAR(100),
  scheduled_at TIMESTAMP,
  completed_at TIMESTAMP,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 4.3 Call management tables
```sql
-- Calls table for tracking all communications
calls (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES customers(id),
  agent_id UUID REFERENCES users(id),
  call_sid VARCHAR(100) UNIQUE, -- Twilio call SID
  direction call_direction_enum NOT NULL,
  from_number VARCHAR(20) NOT NULL,
  to_number VARCHAR(20) NOT NULL,
  status call_status_enum DEFAULT 'initiated',
  duration INTEGER DEFAULT 0, -- in seconds
  recording_url VARCHAR(500),
  disposition call_disposition_enum,
  notes TEXT,
  started_at TIMESTAMP,
  ended_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Call queue for managing incoming calls
call_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  call_id UUID REFERENCES calls(id) ON DELETE CASCADE,
  queue_name VARCHAR(100) NOT NULL,
  priority INTEGER DEFAULT 0,
  wait_time INTEGER DEFAULT 0,
  assigned_agent_id UUID REFERENCES users(id),
  status queue_status_enum DEFAULT 'waiting',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 4.4 Service and invoicing tables
```sql
-- Services offered by the organization
services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(100),
  pricing_model pricing_model_enum NOT NULL,
  base_price DECIMAL(10,2),
  hourly_rate DECIMAL(10,2),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Service requests from customers
service_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id),
  assigned_agent_id UUID REFERENCES users(id),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  priority priority_enum DEFAULT 'medium',
  status service_status_enum DEFAULT 'pending',
  scheduled_at TIMESTAMP,
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  location JSONB,
  estimated_duration INTEGER, -- in minutes
  actual_duration INTEGER, -- in minutes
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Invoices for billing customers
invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
  service_request_id UUID REFERENCES service_requests(id),
  agent_id UUID REFERENCES users(id),
  invoice_number VARCHAR(50) UNIQUE NOT NULL,
  subtotal DECIMAL(10,2) NOT NULL,
  tax_amount DECIMAL(10,2) DEFAULT 0,
  total_amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  status invoice_status_enum DEFAULT 'draft',
  payment_method VARCHAR(50),
  payment_intent_id VARCHAR(100), -- Stripe payment intent ID
  paid_at TIMESTAMP,
  due_date DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Invoice line items
invoice_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id),
  description VARCHAR(255) NOT NULL,
  quantity DECIMAL(8,2) DEFAULT 1,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 5. Indexing strategy
### 5.1 Primary indexes
- **Performance-critical queries**: Indexes on frequently queried columns
- **Foreign key indexes**: Automatic indexing for all foreign key relationships
- **Composite indexes**: Multi-column indexes for complex query patterns
- **Partial indexes**: Conditional indexes for filtered queries
- **Text search indexes**: GIN indexes for full-text search capabilities

### 5.2 Specific index recommendations
```sql
-- User lookup indexes
CREATE INDEX idx_users_email ON users(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_users_organization ON user_organizations(organization_id, role);

-- Customer search indexes
CREATE INDEX idx_customers_phone ON customers(phone) WHERE deleted_at IS NULL;
CREATE INDEX idx_customers_org_status ON customers(organization_id, lead_status);

-- Call performance indexes
CREATE INDEX idx_calls_agent_date ON calls(agent_id, created_at DESC);
CREATE INDEX idx_calls_customer ON calls(customer_id, created_at DESC);
CREATE INDEX idx_calls_org_status ON calls(organization_id, status);

-- Invoice and billing indexes
CREATE INDEX idx_invoices_customer ON invoices(customer_id, status);
CREATE INDEX idx_invoices_agent_date ON invoices(agent_id, created_at DESC);
CREATE INDEX idx_service_requests_agent ON service_requests(assigned_agent_id, status);
```

## 6. Data integrity and constraints
### 6.1 Referential integrity
- **Foreign key constraints**: Enforce relationships between tables
- **Cascade rules**: Appropriate CASCADE, RESTRICT, and SET NULL rules
- **Check constraints**: Validate data ranges and business rules
- **Unique constraints**: Prevent duplicate data where required

### 6.2 Business rule constraints
```sql
-- Ensure invoice totals are calculated correctly
ALTER TABLE invoices ADD CONSTRAINT chk_invoice_total 
CHECK (total_amount = subtotal + tax_amount);

-- Ensure service request dates are logical
ALTER TABLE service_requests ADD CONSTRAINT chk_service_dates
CHECK (completed_at IS NULL OR completed_at >= started_at);

-- Ensure call duration is non-negative
ALTER TABLE calls ADD CONSTRAINT chk_call_duration
CHECK (duration >= 0);
```

## 7. Performance optimization
### 7.1 Query optimization
- **Explain plan analysis**: Regular query performance analysis
- **Statistics updates**: Automated statistics collection for query planner
- **Connection pooling**: Efficient database connection management
- **Read replicas**: Separate read workloads from write operations

### 7.2 Partitioning strategy
```sql
-- Partition large tables by date for better performance
CREATE TABLE calls_2024 PARTITION OF calls
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- Partition by organization for multi-tenant isolation
CREATE TABLE customer_interactions_org1 PARTITION OF customer_interactions
FOR VALUES IN ('org1-uuid');
```

## 8. Security and compliance
### 8.1 Data security
- **Row-level security**: Tenant isolation through RLS policies
- **Encryption**: Sensitive data encryption at rest and in transit
- **Access controls**: Role-based database access permissions
- **Audit logging**: Database-level audit trail for sensitive operations

### 8.2 Privacy compliance
- **PII identification**: Clear marking of personally identifiable information
- **Data retention**: Automated data purging based on retention policies
- **Anonymization**: Data anonymization procedures for analytics
- **Right to deletion**: Support for GDPR deletion requests

## 9. Migration and deployment
### 9.1 Schema versioning
- **Migration scripts**: Version-controlled database schema changes
- **Rollback procedures**: Safe rollback mechanisms for failed deployments
- **Data seeding**: Initial data population scripts
- **Environment consistency**: Identical schemas across environments

### 9.2 Backup and recovery
- **Automated backups**: Regular full and incremental backups
- **Point-in-time recovery**: Ability to restore to specific timestamps
- **Cross-region replication**: Geographic backup distribution
- **Recovery testing**: Regular backup restoration testing

## 10. User stories
### 10.1 Multi-tenant Data Isolation
- **ID**: US-DB-001
- **Description**: As a system administrator, I want to ensure complete data isolation between different organizations using the platform.
- **Acceptance criteria**:
  - Each organization's data is logically separated in the database
  - Queries automatically filter by organization context
  - Cross-organization data access is prevented at the database level
  - Performance is maintained despite multi-tenancy overhead

### 10.2 Efficient Call History Queries
- **ID**: US-DB-002
- **Description**: As a CSR, I want to quickly access customer call history and interaction records.
- **Acceptance criteria**:
  - Call history queries return results in under 100ms
  - Customer interaction timeline is efficiently retrievable
  - Search functionality works across multiple fields
  - Pagination handles large datasets efficiently

### 10.3 Real-time Agent Performance Tracking
- **ID**: US-DB-003
- **Description**: As a team leader, I want to track agent performance metrics in real-time.
- **Acceptance criteria**:
  - Agent KPI calculations are efficient and accurate
  - Performance data is updated in real-time as calls complete
  - Historical performance trends are easily accessible
  - Reporting queries don't impact operational performance

### 10.4 Invoice and Payment Processing
- **ID**: US-DB-004
- **Description**: As an agent, I want to create invoices and track payment status efficiently.
- **Acceptance criteria**:
  - Invoice creation includes all necessary line items and calculations
  - Payment status updates are reflected immediately
  - Commission calculations are automated and accurate
  - Invoice history and reporting are easily accessible

### 10.5 Data Backup and Recovery
- **ID**: US-DB-005
- **Description**: As a system administrator, I want reliable backup and recovery procedures for business continuity.
- **Acceptance criteria**:
  - Automated daily backups with verification
  - Point-in-time recovery capability for data restoration
  - Backup integrity is regularly tested and validated
  - Recovery procedures are documented and tested
