<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Wattatech Mobile</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
</head>
<body class="bg-gray-950 text-white font-sans">
    <div class="flex justify-center items-center min-h-screen p-4">
        <div class="mobile-frame">
            <div class="mobile-screen">
                <!-- Mobile Status Bar -->
                <div class="mobile-status-bar">
                    <span>9:41</span>
                    <span class="flex items-center space-x-1">
                        <i class="fas fa-signal text-xs"></i>
                        <i class="fas fa-wifi text-xs"></i>
                        <i class="fas fa-battery-three-quarters text-xs"></i>
                    </span>
                </div>

                <!-- Mobile Content -->
                <div class="mobile-content bg-gray-900 flex flex-col">
                    <!-- Header -->
                    <div class="text-center pt-8 pb-6">
                        <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-phone text-white text-2xl"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-white mb-1">Wattatech</h1>
                        <p class="text-gray-400 text-sm">Enterprise Service Platform</p>
                    </div>

                    <!-- Login Form -->
                    <div class="flex-1 px-6">
                        <div class="mb-6">
                            <h2 class="text-xl font-semibold text-white mb-2">Welcome back</h2>
                            <p class="text-gray-400 text-sm">Sign in to continue</p>
                        </div>

                        <form class="space-y-4" id="mobileLoginForm">
                            <div>
                                <label for="mobile-email" class="block text-sm font-medium text-gray-300 mb-2">
                                    Email
                                </label>
                                <div class="relative">
                                    <input 
                                        id="mobile-email" 
                                        name="email" 
                                        type="email" 
                                        required 
                                        class="w-full px-4 py-4 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-base"
                                        placeholder="Enter your email"
                                    >
                                    <i class="fas fa-envelope absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                </div>
                            </div>

                            <div>
                                <label for="mobile-password" class="block text-sm font-medium text-gray-300 mb-2">
                                    Password
                                </label>
                                <div class="relative">
                                    <input 
                                        id="mobile-password" 
                                        name="password" 
                                        type="password" 
                                        required 
                                        class="w-full px-4 py-4 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-base"
                                        placeholder="Enter your password"
                                    >
                                    <button 
                                        type="button" 
                                        class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 p-1"
                                        onclick="toggleMobilePassword()"
                                    >
                                        <i class="fas fa-eye" id="mobilePasswordToggle"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between pt-2">
                                <label class="flex items-center">
                                    <input 
                                        type="checkbox" 
                                        class="h-4 w-4 text-primary-600 bg-gray-800 border-gray-700 rounded focus:ring-primary-500"
                                    >
                                    <span class="ml-2 text-sm text-gray-300">Remember me</span>
                                </label>
                                <a href="forgot-password.html" class="text-sm text-primary-400 hover:text-primary-300">
                                    Forgot?
                                </a>
                            </div>

                            <div class="pt-4">
                                <button 
                                    type="submit" 
                                    class="w-full py-4 px-4 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900"
                                >
                                    <span class="flex items-center justify-center">
                                        <i class="fas fa-sign-in-alt mr-2"></i>
                                        Sign In
                                    </span>
                                </button>
                            </div>

                            <!-- Biometric Login -->
                            <div class="pt-4">
                                <button 
                                    type="button" 
                                    class="w-full py-4 px-4 bg-gray-800 hover:bg-gray-700 text-gray-300 font-medium rounded-xl transition-colors border border-gray-700"
                                    onclick="simulateBiometric()"
                                >
                                    <span class="flex items-center justify-center">
                                        <i class="fas fa-fingerprint mr-2"></i>
                                        Use Touch ID
                                    </span>
                                </button>
                            </div>

                            <div class="relative pt-4">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-700"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-gray-900 text-gray-400">Or</span>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-3 pt-4">
                                <button 
                                    type="button" 
                                    class="py-3 px-4 border border-gray-700 rounded-xl bg-gray-800 text-gray-300 hover:bg-gray-700 transition-colors"
                                >
                                    <i class="fab fa-google text-red-400"></i>
                                </button>
                                <button 
                                    type="button" 
                                    class="py-3 px-4 border border-gray-700 rounded-xl bg-gray-800 text-gray-300 hover:bg-gray-700 transition-colors"
                                >
                                    <i class="fab fa-apple text-white"></i>
                                </button>
                            </div>
                        </form>

                        <!-- Quick Role Access -->
                        <div class="mt-6 p-4 bg-gray-800/50 rounded-xl border border-gray-700">
                            <p class="text-xs text-gray-400 mb-3 text-center">Quick Access</p>
                            <div class="grid grid-cols-3 gap-2">
                                <button onclick="mobileDemo('csr')" class="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-center transition-colors">
                                    <i class="fas fa-headset text-primary-400 text-lg mb-1 block"></i>
                                    <span class="text-xs text-gray-300">CSR</span>
                                </button>
                                <button onclick="mobileDemo('onsite_agent')" class="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-center transition-colors">
                                    <i class="fas fa-tools text-primary-400 text-lg mb-1 block"></i>
                                    <span class="text-xs text-gray-300">Agent</span>
                                </button>
                                <button onclick="mobileDemo('customer')" class="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-center transition-colors">
                                    <i class="fas fa-user text-primary-400 text-lg mb-1 block"></i>
                                    <span class="text-xs text-gray-300">Customer</span>
                                </button>
                            </div>
                        </div>

                        <div class="text-center pt-6 pb-4">
                            <p class="text-sm text-gray-400">
                                Don't have an account?
                                <a href="register.html" class="text-primary-400 hover:text-primary-300 font-medium">
                                    Sign up
                                </a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Mobile Home Indicator -->
                <div class="mobile-home-indicator"></div>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        function toggleMobilePassword() {
            const passwordInput = document.getElementById('mobile-password');
            const passwordToggle = document.getElementById('mobilePasswordToggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.classList.remove('fa-eye');
                passwordToggle.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordToggle.classList.remove('fa-eye-slash');
                passwordToggle.classList.add('fa-eye');
            }
        }

        function simulateBiometric() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Authenticating...';
            button.disabled = true;
            
            setTimeout(() => {
                // Simulate successful biometric auth
                window.location.href = '../../csr/mobile/dashboard.html';
            }, 2000);
        }

        function mobileDemo(role) {
            const roleRoutes = {
                'csr': '../../csr/mobile/dashboard.html',
                'onsite_agent': '../../onsite-agent/mobile/dashboard.html',
                'customer': '../../customer/mobile/dashboard.html'
            };
            
            const button = event.target.closest('button');
            const originalContent = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin text-primary-400 text-lg mb-1 block"></i><span class="text-xs text-gray-300">Loading...</span>';
            button.disabled = true;
            
            setTimeout(() => {
                window.location.href = roleRoutes[role];
            }, 1500);
        }

        // Mobile form submission
        document.getElementById('mobileLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('mobile-email').value;
            const password = document.getElementById('mobile-password').value;
            
            if (email && password) {
                const submitButton = e.target.querySelector('button[type="submit"]');
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Signing in...';
                submitButton.disabled = true;
                
                setTimeout(() => {
                    window.location.href = '../../csr/mobile/dashboard.html';
                }, 2000);
            }
        });

        // Add mobile-specific touch interactions
        document.addEventListener('touchstart', function(e) {
            // Add touch feedback
            if (e.target.matches('button, .btn')) {
                e.target.style.transform = 'scale(0.98)';
            }
        });

        document.addEventListener('touchend', function(e) {
            // Remove touch feedback
            if (e.target.matches('button, .btn')) {
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 100);
            }
        });
    </script>
</body>
</html>
