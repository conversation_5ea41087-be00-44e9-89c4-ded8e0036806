<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone UI Component - Wattatech CSR</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a', 950: '#172554'
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../../assets/css/styles.css" rel="stylesheet">
    <style>
        .phone-ui {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 320px;
            background: linear-gradient(145deg, #1f2937, #111827);
            border: 1px solid #374151;
            border-radius: 16px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .phone-ui.minimized {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
        }
        .phone-ui.call-active {
            border-color: #10b981;
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3), 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }
        .phone-ui.call-ringing {
            border-color: #f59e0b;
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.3), 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            animation: pulse 1s infinite;
        }
        .phone-ui.call-hold {
            border-color: #ef4444;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3), 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }
        .dialer-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #374151;
            border: 1px solid #4b5563;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        .dialer-button:hover {
            background: #4b5563;
            transform: scale(1.05);
        }
        .dialer-button:active {
            transform: scale(0.95);
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        .call-timer {
            font-variant-numeric: tabular-nums;
        }
    </style>
</head>
<body class="bg-gray-950 text-white font-sans min-h-screen p-8">
    <!-- Demo Container -->
    <div class="max-w-4xl mx-auto">
        <div class="mb-8">
            <a href="dashboard.html" class="text-primary-400 hover:text-primary-300 mb-4 inline-flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
            <h1 class="text-3xl font-bold text-white mb-2">Phone UI Component</h1>
            <p class="text-gray-400">Floating phone interface for CSR call handling</p>
        </div>

        <!-- Demo Controls -->
        <div class="bg-gray-900 border border-gray-800 rounded-lg p-6 mb-8">
            <h2 class="text-lg font-semibold text-white mb-4">Demo Controls</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="simulateIncomingCall()" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-phone-alt mr-2"></i>
                    Incoming Call
                </button>
                <button onclick="simulateOutgoingCall()" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-phone mr-2"></i>
                    Outgoing Call
                </button>
                <button onclick="toggleMinimize()" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">
                    <i class="fas fa-compress mr-2"></i>
                    Toggle Minimize
                </button>
                <button onclick="resetPhone()" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                    <i class="fas fa-power-off mr-2"></i>
                    Reset
                </button>
            </div>
        </div>

        <!-- Features Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Phone States</h3>
                <ul class="space-y-2 text-gray-300">
                    <li class="flex items-center"><i class="fas fa-circle text-gray-500 mr-2"></i> Idle - Ready to receive calls</li>
                    <li class="flex items-center"><i class="fas fa-circle text-yellow-500 mr-2"></i> Ringing - Incoming call alert</li>
                    <li class="flex items-center"><i class="fas fa-circle text-green-500 mr-2"></i> Active - Call in progress</li>
                    <li class="flex items-center"><i class="fas fa-circle text-red-500 mr-2"></i> Hold - Call on hold</li>
                    <li class="flex items-center"><i class="fas fa-circle text-blue-500 mr-2"></i> Dialing - Outbound call</li>
                </ul>
            </div>
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Features</h3>
                <ul class="space-y-2 text-gray-300">
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Floating interface</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Minimize/maximize</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Call timer</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Touch-tone dialer</li>
                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i> Call controls</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Floating Phone UI -->
    <div id="phoneUI" class="phone-ui">
        <!-- Minimized State -->
        <div id="phoneMinimized" class="hidden w-full h-full flex items-center justify-center">
            <i class="fas fa-phone text-white text-xl"></i>
        </div>

        <!-- Full Phone Interface -->
        <div id="phoneExpanded" class="p-4">
            <!-- Header -->
            <div class="flex items-center justify-between mb-4">
                <h4 class="text-sm font-semibold text-white">Phone</h4>
                <div class="flex items-center space-x-2">
                    <div id="phoneStatus" class="flex items-center">
                        <div class="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                        <span class="text-xs text-gray-400">Ready</span>
                    </div>
                    <button onclick="toggleMinimize()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
            
            <!-- Idle State -->
            <div id="phoneIdle" class="text-center">
                <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-phone text-white text-xl"></i>
                </div>
                <p class="text-sm text-gray-300 mb-4">Ready to take calls</p>
                <button onclick="showDialer()" class="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors mb-2">
                    <i class="fas fa-th mr-2"></i>
                    Open Dialer
                </button>
                <button onclick="simulateIncomingCall()" class="w-full py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-colors">
                    <i class="fas fa-phone mr-2"></i>
                    Simulate Call
                </button>
            </div>

            <!-- Dialer State -->
            <div id="phoneDialer" class="hidden">
                <div class="mb-4">
                    <input type="text" id="dialerInput" class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white text-center text-lg font-mono" placeholder="Enter number" readonly>
                </div>
                <div class="grid grid-cols-3 gap-3 mb-4">
                    <button onclick="dialNumber('1')" class="dialer-button">1</button>
                    <button onclick="dialNumber('2')" class="dialer-button">2<br><span class="text-xs">ABC</span></button>
                    <button onclick="dialNumber('3')" class="dialer-button">3<br><span class="text-xs">DEF</span></button>
                    <button onclick="dialNumber('4')" class="dialer-button">4<br><span class="text-xs">GHI</span></button>
                    <button onclick="dialNumber('5')" class="dialer-button">5<br><span class="text-xs">JKL</span></button>
                    <button onclick="dialNumber('6')" class="dialer-button">6<br><span class="text-xs">MNO</span></button>
                    <button onclick="dialNumber('7')" class="dialer-button">7<br><span class="text-xs">PQRS</span></button>
                    <button onclick="dialNumber('8')" class="dialer-button">8<br><span class="text-xs">TUV</span></button>
                    <button onclick="dialNumber('9')" class="dialer-button">9<br><span class="text-xs">WXYZ</span></button>
                    <button onclick="dialNumber('*')" class="dialer-button">*</button>
                    <button onclick="dialNumber('0')" class="dialer-button">0<br><span class="text-xs">+</span></button>
                    <button onclick="dialNumber('#')" class="dialer-button">#</button>
                </div>
                <div class="grid grid-cols-3 gap-2">
                    <button onclick="clearDialer()" class="py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm">
                        <i class="fas fa-backspace"></i>
                    </button>
                    <button onclick="makeCall()" class="py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm">
                        <i class="fas fa-phone"></i>
                    </button>
                    <button onclick="hideDialer()" class="py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Incoming Call State -->
            <div id="phoneIncoming" class="hidden text-center">
                <div class="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
                    <i class="fas fa-phone text-white text-2xl"></i>
                </div>
                <p class="text-lg font-semibold text-white mb-1" id="incomingCallerName">William Davis</p>
                <p class="text-sm text-gray-400 mb-2" id="incomingCallerNumber">+****************</p>
                <p class="text-xs text-gray-400 mb-6">Incoming Call</p>
                
                <div class="grid grid-cols-2 gap-4">
                    <button onclick="declineCall()" class="py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-phone-slash text-xl"></i>
                    </button>
                    <button onclick="answerCall()" class="py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                        <i class="fas fa-phone text-xl"></i>
                    </button>
                </div>
            </div>

            <!-- Active Call State -->
            <div id="phoneActive" class="hidden text-center">
                <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-phone text-white text-xl"></i>
                </div>
                <p class="text-sm font-medium text-white mb-1" id="activeCallerName">William Davis</p>
                <p class="text-xs text-gray-400 mb-2" id="activeCallerNumber">+****************</p>
                <p class="text-xs text-gray-400 mb-4 call-timer" id="callTimer">00:00</p>
                
                <div class="grid grid-cols-3 gap-2 mb-4">
                    <button onclick="muteCall()" id="muteBtn" class="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm transition-colors">
                        <i class="fas fa-microphone"></i>
                    </button>
                    <button onclick="holdCall()" id="holdBtn" class="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm transition-colors">
                        <i class="fas fa-pause"></i>
                    </button>
                    <button onclick="transferCall()" class="p-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-white text-sm transition-colors">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
                
                <button onclick="endCall()" class="w-full py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors">
                    <i class="fas fa-phone-slash mr-2"></i>
                    End Call
                </button>
            </div>
        </div>
    </div>

    <script src="../../assets/js/app.js"></script>
    <script>
        let callTimer = 0;
        let callInterval;
        let isMinimized = false;
        let isMuted = false;
        let isOnHold = false;

        function toggleMinimize() {
            const phoneUI = document.getElementById('phoneUI');
            const minimized = document.getElementById('phoneMinimized');
            const expanded = document.getElementById('phoneExpanded');
            
            isMinimized = !isMinimized;
            
            if (isMinimized) {
                phoneUI.classList.add('minimized');
                minimized.classList.remove('hidden');
                expanded.classList.add('hidden');
            } else {
                phoneUI.classList.remove('minimized');
                minimized.classList.add('hidden');
                expanded.classList.remove('hidden');
            }
        }

        function updatePhoneStatus(status, color) {
            const statusElement = document.getElementById('phoneStatus');
            const dot = statusElement.querySelector('.w-2');
            const text = statusElement.querySelector('span');
            
            dot.className = `w-2 h-2 rounded-full mr-2 bg-${color}-500`;
            text.textContent = status;
        }

        function showState(stateId) {
            const states = ['phoneIdle', 'phoneDialer', 'phoneIncoming', 'phoneActive'];
            states.forEach(state => {
                document.getElementById(state).classList.add('hidden');
            });
            document.getElementById(stateId).classList.remove('hidden');
        }

        function showDialer() {
            showState('phoneDialer');
            updatePhoneStatus('Dialing', 'blue');
        }

        function hideDialer() {
            showState('phoneIdle');
            updatePhoneStatus('Ready', 'gray');
            clearDialer();
        }

        function dialNumber(number) {
            const input = document.getElementById('dialerInput');
            input.value += number;
        }

        function clearDialer() {
            const input = document.getElementById('dialerInput');
            if (input.value.length > 0) {
                input.value = input.value.slice(0, -1);
            }
        }

        function makeCall() {
            const number = document.getElementById('dialerInput').value;
            if (number) {
                simulateOutgoingCall();
            }
        }

        function simulateIncomingCall() {
            const phoneUI = document.getElementById('phoneUI');
            phoneUI.classList.add('call-ringing');
            showState('phoneIncoming');
            updatePhoneStatus('Ringing', 'yellow');
            
            // Auto-expand if minimized
            if (isMinimized) {
                toggleMinimize();
            }
        }

        function simulateOutgoingCall() {
            showDialer();
            setTimeout(() => {
                answerCall();
            }, 2000);
        }

        function answerCall() {
            const phoneUI = document.getElementById('phoneUI');
            phoneUI.classList.remove('call-ringing');
            phoneUI.classList.add('call-active');
            showState('phoneActive');
            updatePhoneStatus('Active', 'green');
            
            // Start call timer
            callTimer = 0;
            callInterval = setInterval(() => {
                callTimer++;
                const minutes = Math.floor(callTimer / 60);
                const seconds = callTimer % 60;
                document.getElementById('callTimer').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function declineCall() {
            resetPhone();
        }

        function endCall() {
            clearInterval(callInterval);
            resetPhone();
            
            // Show call disposition modal after a brief delay
            setTimeout(() => {
                if (confirm('Call ended. Would you like to add a disposition?')) {
                    window.location.href = 'call-disposition.html';
                }
            }, 500);
        }

        function muteCall() {
            isMuted = !isMuted;
            const muteBtn = document.getElementById('muteBtn');
            
            if (isMuted) {
                muteBtn.classList.add('bg-red-600');
                muteBtn.classList.remove('bg-gray-700');
                muteBtn.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                updatePhoneStatus('Muted', 'red');
            } else {
                muteBtn.classList.remove('bg-red-600');
                muteBtn.classList.add('bg-gray-700');
                muteBtn.innerHTML = '<i class="fas fa-microphone"></i>';
                updatePhoneStatus('Active', 'green');
            }
        }

        function holdCall() {
            isOnHold = !isOnHold;
            const holdBtn = document.getElementById('holdBtn');
            const phoneUI = document.getElementById('phoneUI');
            
            if (isOnHold) {
                holdBtn.classList.add('bg-yellow-600');
                holdBtn.classList.remove('bg-gray-700');
                holdBtn.innerHTML = '<i class="fas fa-play"></i>';
                phoneUI.classList.add('call-hold');
                phoneUI.classList.remove('call-active');
                updatePhoneStatus('On Hold', 'red');
            } else {
                holdBtn.classList.remove('bg-yellow-600');
                holdBtn.classList.add('bg-gray-700');
                holdBtn.innerHTML = '<i class="fas fa-pause"></i>';
                phoneUI.classList.remove('call-hold');
                phoneUI.classList.add('call-active');
                updatePhoneStatus('Active', 'green');
            }
        }

        function transferCall() {
            if (confirm('Transfer call to on-site agent?')) {
                endCall();
            }
        }

        function resetPhone() {
            const phoneUI = document.getElementById('phoneUI');
            phoneUI.classList.remove('call-active', 'call-ringing', 'call-hold');
            showState('phoneIdle');
            updatePhoneStatus('Ready', 'gray');
            clearInterval(callInterval);
            callTimer = 0;
            isMuted = false;
            isOnHold = false;
            
            // Reset button states
            const muteBtn = document.getElementById('muteBtn');
            const holdBtn = document.getElementById('holdBtn');
            muteBtn.classList.remove('bg-red-600');
            muteBtn.classList.add('bg-gray-700');
            muteBtn.innerHTML = '<i class="fas fa-microphone"></i>';
            holdBtn.classList.remove('bg-yellow-600');
            holdBtn.classList.add('bg-gray-700');
            holdBtn.innerHTML = '<i class="fas fa-pause"></i>';
            
            clearDialer();
        }

        // Initialize phone UI
        document.addEventListener('DOMContentLoaded', function() {
            resetPhone();
        });
    </script>
</body>
</html>
